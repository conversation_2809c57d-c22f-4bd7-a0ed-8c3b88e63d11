<template>
  <div style="height: 100%">
    <v-card class="pa-4 pb-1 team-updates-card rounded-lg">
      <!-- My Team Updates title -->
      <v-row>
        <v-col cols="12" class="py-2">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="red"
              :size="22"
              class="mr-2"
            />
            <span class="ml-2 text-primary text-h6 font-weight-bold">
              {{ $t("dashboard.myTeamUpdates") }}
            </span>
          </div>
        </v-col>
      </v-row>

      <!-- Team Updates loading screen -->
      <v-row v-if="isTeamUpdatesLoading" class="pa-1 pb-0">
        <v-col cols="12" class="py-0">
          <div class="team-updates-content">
            <v-row v-for="index in 2" :key="index" class="mb-3">
              <v-col cols="2">
                <v-skeleton-loader type="avatar" />
              </v-col>
              <v-col cols="5">
                <v-skeleton-loader type="text@3" />
              </v-col>
            </v-row>
          </div>
        </v-col>
      </v-row>

      <!-- Team Updates Error And Empty screen -->
      <v-row
        v-else-if="errorInTeamUpdates || teamUpdatesLength === 0"
        class="pa-1 pb-0"
      >
        <v-col cols="12" class="py-0">
          <div class="d-flex align-center team-updates-content">
            <NoDataCardWithQuotes
              id="admin_dashboard_team_updates_refresh"
              image-name="admin-dashboard/team-updates"
              :primary-bold-text="$t('dashboard.aKeyToSuccess')"
              :text-message="$t('dashboard.assembleStrongStableManagementTeam')"
              :is-small-card="false"
              :card-type="errorInTeamUpdates ? 'error' : 'no-data'"
              :error-content="
                errorInTeamUpdates ? $t('dashboard.technicalDifficulties') : ''
              "
              image-size="80%"
              :is-show-image="windowWidth > 960"
              @refresh-triggered="refreshTeamUpdates()"
            />
          </div>
        </v-col>
      </v-row>

      <!-- Team Updates Content -->
      <v-row v-else class="pa-1 pb-0">
        <v-col cols="12" class="py-0">
          <perfect-scrollbar
            class="w-100 overflow-y-auto overflow-x-hidden team-updates-scrollbar"
          >
            <div class="team-updates-content">
              <v-list class="pa-0">
                <!-- Probation Wizards -->
                <v-row
                  v-if="probationList.length > 0"
                  class="align-center"
                  style="width: 100%; margin: 0px"
                >
                  <v-col
                    cols="12"
                    xs="12"
                    :sm="windowWidth > 700 ? 3 : 4"
                    md="4"
                    :lg="windowWidth > 1580 ? 3 : 4"
                    class="pa-1"
                  >
                    <CardIconTitle
                      card-title="In Probation"
                      :count="probationList.length"
                      icon-name="fas fa-handshake"
                      icon-color="purple"
                      icon-background="purple-lighten-5"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    xs="12"
                    :sm="windowWidth > 700 ? 9 : 8"
                    md="8"
                    :lg="windowWidth > 1580 ? 9 : 8"
                    class="px-1 py-0"
                  >
                    <AvatarSlider :avatar-array="probationList" />
                  </v-col>
                </v-row>

                <!--New Hires Wizards -->
                <v-col
                  v-if="newJoineesList.length > 0 && probationList.length > 0"
                  cols="12"
                >
                  <v-divider />
                </v-col>
                <v-row
                  v-if="newJoineesList.length > 0"
                  class="align-center"
                  style="width: 100%; margin: 0px"
                >
                  <v-col
                    cols="12"
                    xs="12"
                    :sm="windowWidth > 700 ? 3 : 4"
                    md="4"
                    :lg="windowWidth > 1580 ? 3 : 4"
                    class="pa-1"
                  >
                    <CardIconTitle
                      card-title="New Hires"
                      :count="newJoineesList.length"
                      icon-name="fas fa-hand-sparkles"
                      icon-color="indigo"
                      icon-background="indigo-lighten-5"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    xs="12"
                    :sm="windowWidth > 700 ? 9 : 8"
                    md="8"
                    :lg="windowWidth > 1580 ? 9 : 8"
                    class="px-1 py-0"
                  >
                    <AvatarSlider :avatar-array="newJoineesList" />
                  </v-col>
                </v-row>

                <!-- Off Boarding -->
                <v-col
                  v-if="
                    offboardingList.length > 0 &&
                    (newJoineesList.length > 0 || probationList.length > 0)
                  "
                  cols="12"
                >
                  <v-divider />
                </v-col>
                <v-row
                  v-if="offboardingList.length > 0"
                  class="align-center"
                  style="width: 100%; margin: 0px"
                >
                  <v-col
                    cols="12"
                    xs="12"
                    :sm="windowWidth > 700 ? 3 : 4"
                    md="4"
                    :lg="windowWidth > 1580 ? 3 : 4"
                    class="pa-1"
                  >
                    <CardIconTitle
                      card-title="Offboarding"
                      :count="offboardingList.length"
                      icon-name="fas fa-running"
                      icon-color="red"
                      icon-background="red-lighten-5"
                    />
                  </v-col>
                  <v-col
                    cols="12"
                    xs="12"
                    :sm="windowWidth > 700 ? 9 : 8"
                    md="8"
                    :lg="windowWidth > 1580 ? 9 : 8"
                    class="px-1 py-0"
                  >
                    <AvatarSlider :avatar-array="offboardingList" />
                  </v-col>
                </v-row>
              </v-list>
            </div>
          </perfect-scrollbar>
        </v-col>
      </v-row>
    </v-card>
  </div>
</template>

<script>
import { GET_TEAM_UPDATES } from "@/graphql/dashboard/dashboardQueries";

//components
import AvatarSlider from "@/components/helper-components/AvatarSlider";
import CardIconTitle from "@/components/helper-components/CardIconTitle";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes";
export default {
  name: "MyTeamUpdates",

  components: {
    AvatarSlider,
    CardIconTitle,
    NoDataCardWithQuotes,
  },

  data() {
    return {
      isTeamUpdatesLoading: true,
      errorInTeamUpdates: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    getTeamUpdates() {
      return this.$store.state.dashboard.myTeamUpdates;
    },
    //formatting probation list data to use it inside avatarslider component incase of data exist
    probationList() {
      if (this.getTeamUpdates?.probation) {
        let parsedProbation = JSON.parse(this.getTeamUpdates.probation) || [];
        let probationArray = [];
        for (var index = 0; index < parsedProbation.length; index++) {
          let probations = {
            title: parsedProbation[index]?.Employee_First_Name,
            subtitle:
              parsedProbation[index]?.Date_of_Join +
              "-" +
              parsedProbation[index]?.Probation_Date,
            photoPath: parsedProbation[index]?.Profile_Pic,
          };
          probationArray.push(probations);
        }
        return probationArray;
      } else {
        return [];
      }
    },

    //formatting newJoinees list data to use it inside avatarslider component incase of data exist
    newJoineesList() {
      if (this.getTeamUpdates?.newJoining) {
        let parsednewJoinees = JSON.parse(this.getTeamUpdates.newJoining);
        let newJoineesArray = [];
        for (var index = 0; index < parsednewJoinees.length; index++) {
          let newJoiners = {
            title: parsednewJoinees[index]?.Employee_First_Name,
            subtitle: parsednewJoinees[index]?.Date_of_Join,
            photoPath: parsednewJoinees[index]?.Profile_Pic,
          };
          newJoineesArray.push(newJoiners);
        }
        return newJoineesArray;
      } else {
        return [];
      }
    },

    //formatting offboarding user list data to use it inside avatarslider component incase of data exist
    offboardingList() {
      if (this.getTeamUpdates?.offboarding) {
        let parsedOffboardings = JSON.parse(this.getTeamUpdates.offboarding);
        let offboardingArray = [];
        for (var index = 0; index < parsedOffboardings.length; index++) {
          let offboarding = {
            title: parsedOffboardings[index]?.Employee_First_Name,
            subtitle:
              parsedOffboardings[index]?.Notice_Start_Date +
              "-" +
              parsedOffboardings[index]?.Resignation_Date,
            photoPath: parsedOffboardings[index]?.Profile_Pic,
          };
          offboardingArray.push(offboarding);
        }
        return offboardingArray;
      } else {
        return [];
      }
    },
    //find count of totol team updates
    teamUpdatesLength() {
      let teamUpdateLength =
        this.probationList.length +
        this.offboardingList.length +
        this.newJoineesList.length;
      return teamUpdateLength;
    },
  },

  mounted() {
    this.fetchTeamUpdates();
  },

  methods: {
    fetchTeamUpdates() {
      let vm = this;
      vm.isTeamUpdatesLoading = true;
      vm.errorInTeamUpdates = false;
      vm.$apollo
        .query({
          query: GET_TEAM_UPDATES,
          client: "apolloClientC",
        })
        .then((response) => {
          if (
            response.data &&
            response.data.myTeamUpdates &&
            response.data.myTeamUpdates.myTeamUpdates &&
            !response.data.myTeamUpdates.errorCode
          ) {
            let tempData = response.data.myTeamUpdates.myTeamUpdates || [];
            this.$store.commit("dashboard/MY_TEAM_UPDATES", tempData);
          } else {
            vm.errorInTeamUpdates = true;
          }
          vm.isTeamUpdatesLoading = false;
        })
        .catch(() => {
          vm.errorInTeamUpdates = true;
          vm.isTeamUpdatesLoading = false;
        });
    },

    // Refresh team updates data
    refreshTeamUpdates() {
      this.errorInTeamUpdates = false;
      this.fetchTeamUpdates();
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
.team-updates-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.team-updates-content {
  min-height: 250px;
}

/* Team updates scrollbar height - responsive */
.team-updates-scrollbar {
  min-height: 280px;
  max-height: 300px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .team-updates-scrollbar {
    min-height: 280px;
    max-height: 500px;
  }

  .team-updates-content {
    min-height: 200px;
    max-height: 500px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .team-updates-scrollbar {
    min-height: 250px;
    max-height: 250px;
  }

  .team-updates-content {
    min-height: 225px;
  }
}
</style>
