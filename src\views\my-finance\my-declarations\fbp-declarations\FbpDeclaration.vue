<template>
  <div>
    <AppTopBarTab
      :tabs-list="mainTabs"
      :current-tab="currentTabItem"
      :center-tab="true"
      @tab-clicked="onTabChange($event)"
    >
    </AppTopBarTab>
    <v-container fluid class="fbp-declaration-container">
      <v-window v-model="currentTabItem" v-if="formAccess?.view">
        <v-window-item :value="currentTabItem">
          <div v-if="listLoading" class="mt-3">
            <!-- Skeleton loaders -->
            <v-skeleton-loader
              ref="skeleton1"
              type="table-heading"
              class="mx-auto"
            ></v-skeleton-loader>
            <div v-for="i in 3" :key="i" class="mt-4">
              <v-skeleton-loader
                ref="skeleton2"
                type="list-item-avatar"
                class="mx-auto"
              ></v-skeleton-loader>
            </div>
          </div>
          <AppFetchErrorScreen
            v-else-if="isErrorInList"
            :content="errorContent"
            icon-name="fas fa-redo-alt"
            image-name="common/human-error-image"
            :button-text="'Retry'"
            @button-click="refetchList()"
          />
          <AppFetchErrorScreen
            v-else-if="originalList.length === 0"
            key="no-results-screen"
            main-title=""
            :isSmallImage="true"
            image-name=""
          >
            <template #contentSlot>
              <div style="max-width: 80%">
                <v-row style="background: white" class="rounded-lg pa-5 mb-4">
                  <v-col cols="12">
                    <NotesCard
                      notes="Flexible Benefit Plan (FBP) declaration is not yet configured by your organization. Once configured by your HR/Admin, you will be able to declare your preferred benefits here."
                      backgroundColor="transparent"
                      class="mb-4"
                    ></NotesCard>
                  </v-col>
                  <v-col
                    cols="12"
                    class="d-flex align-center justify-center mb-4"
                  >
                    <v-btn
                      rounded="lg"
                      class="mt-1"
                      color="transparent"
                      variant="flat"
                      :size="isMobileView ? 'small' : 'default'"
                      @click="refetchList()"
                    >
                      <v-icon>fas fa-redo-alt</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </div>
            </template>
          </AppFetchErrorScreen>
          <v-card v-else class="w-100 pa-4" elevation="5">
            <v-card-text>
              <NotesCard
                :heading="`Declare your preferred benefits for the year. The declared amount will be reflected in your payslip. You can update your declaration before ${lockDate}.`"
                image-name=""
                class="mb-4"
              ></NotesCard>
              <EditFbpDeclaration
                v-if="showEditForm"
                :originalList="originalList"
                :employeeId="loginEmployeeId"
                :templateId="templateId"
                :fixedAllowanceArray="fixedAllowanceArray"
                :landingFormName="landedFormName"
                @close-edit-form="showEditForm = false"
                @edit-updated="updateSuccess()"
              />
              <ViewFbpDeclaration
                v-else
                :landingFormName="landedFormName"
                :fixedAllowanceArray="fixedAllowanceArray"
                :originalList="originalList"
                :formAccess="formAccess"
                :lockStatus="lockStatus"
                @open-edit-form="openEditForm()"
              />
            </v-card-text>
          </v-card>
        </v-window-item>
      </v-window>
      <AppAccessDenied v-else />
    </v-container>
  </div>
</template>
<script setup>
import {
  computed,
  onMounted,
  ref,
  defineAsyncComponent,
  getCurrentInstance,
} from "vue";
import { useStore } from "vuex";
import moment from "moment";
import { useRouter } from "vue-router";

const ViewFbpDeclaration = defineAsyncComponent(() =>
  import("./ViewFbpDeclaration.vue")
);
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard.vue")
);
const EditFbpDeclaration = defineAsyncComponent(() =>
  import("./EditFbpDeclaration.vue")
);

const store = useStore();
const instance = getCurrentInstance();
const router = useRouter();

//Tabs Management
const landedFormName = computed(() => {
  return (
    store.getters.formIdBasedAccessRights(388)?.customFormName ||
    "FBP Declaration"
  );
});
const mainTabs = computed(() => {
  let tabs = [];
  let { formsWithAccess } = store.getters.myDeclarationTabs;
  for (let tab of formsWithAccess) {
    if (tab.havingAccess || tab.formName === landedFormName.value)
      tabs.push(tab.formName);
  }
  return tabs;
});
const currentTabItem = ref("");
const onTabChange = (tab) => {
  if (tab !== landedFormName.value) {
    const { formsWithAccess } = store.getters.myDeclarationTabs;
    let clickedForm = formsWithAccess.find((form) => form.formName === tab);
    if (clickedForm) {
      router.push(clickedForm.url);
    }
  }
};

const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});
const showEditForm = ref(false);
const openEditForm = () => {
  showEditForm.value = true;
};
onMounted(() => {
  currentTabItem.value = "tab-" + mainTabs.value.indexOf(landedFormName.value);
  fetchSalary();
  fetchLockStatus();
});

// Access Management
const accessRights = computed(() => {
  return store.getters.formIdBasedAccessRights;
});
const formAccess = computed(() => {
  let formAccessRights = accessRights.value(388);
  if (
    formAccessRights &&
    formAccessRights.accessRights &&
    formAccessRights.accessRights["view"]
  ) {
    return formAccessRights.accessRights;
  } else return false;
});

// List Management
const listLoading = ref(false);
const errorContent = ref("");
const isErrorInList = ref(false);
const originalList = ref([]);
const fixedAllowanceArray = ref([]);
const templateId = ref(null);
const refetchList = () => {
  isErrorInList.value = false;
  errorContent.value = "";
  originalList.value = [];
  fetchSalary();
};
const updateSuccess = () => {
  showEditForm.value = false;
  refetchList();
};
const loginEmployeeId = computed(() => {
  return parseInt(store.state.orgDetails.employeeId, 10);
});
const fetchSalary = async () => {
  listLoading.value = true;
  await store
    .dispatch("getSalaryDetails", {
      formId: 207,
      accessFormId: 388,
      isViewMode: true,
      id: null,
      employeeId: loginEmployeeId.value,
    })
    .then(({ data }) => {
      if (data?.listSalaryTemplateDetails?.templateDetails) {
        let templateDetails = JSON.parse(
          data.listSalaryTemplateDetails.templateDetails
        );
        templateDetails = templateDetails[0];
        templateId.value = templateDetails?.Template_Id;
        if (templateDetails?.allowances?.flexiBenefitPlanArray?.length) {
          originalList.value = templateDetails.allowances.flexiBenefitPlanArray;
          fixedAllowanceArray.value =
            templateDetails.allowances.fixedAllowanceArray;
        } else {
          originalList.value = [];
          fixedAllowanceArray.value = [];
        }
      } else {
        originalList.value = [];
        fixedAllowanceArray.value = [];
      }
      listLoading.value = false;
    })
    .catch((err) => {
      listLoading.value = false;
      store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "salary details",
          isListError: true,
        })
        .then((errorMessages) => {
          errorContent.value = errorMessages;
          isErrorInList.value = true;
        });
    });
};

const lockStatus = ref("");
const lockDate = ref("");
import { RETRIEVE_LOCK_DATE } from "@/graphql/corehr/salaryQueries.js";
const fetchLockStatus = () => {
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_LOCK_DATE,
      client: "apolloClientF",
    })
    .then(({ data }) => {
      if (data?.getFBPDeclarationSettings?.lockStatus) {
        let lock = data.getFBPDeclarationSettings.lockStatus;
        lockStatus.value = lock.status;
        let orgDateFormat = store.state.orgDetails.orgDateFormat;
        lockDate.value = moment(lock.lockDate).format(orgDateFormat);
      }
    })
    .catch((err) => {
      lockStatus.value = "lock";
      let snackbarData = {
        isOpen: true,
        message:
          err?.message ||
          "Something went wrong while fetching the lock status. Please try after some time.",
        type: "warning",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
    });
};
</script>
<style scoped>
.fbp-declaration-container {
  padding: 80px 30px 30px 30px;
}

@media screen and (max-width: 805px) {
  .fbp-declaration-container {
    padding: 80px 20px 20px 20px;
  }
}
</style>
