import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const RETRIEVE_NPS_FUND_RULES = gql`
  query listNpsRules {
    listNpsRules {
      errorCode
      message
      npsRulesDetails
      npsConfigurationDetails
    }
  }
`;
export const RETRIEVE_NPS_FUND_SLABES = gql`
  query listNpsSlabsDetails {
    listNpsSlabsDetails {
      errorCode
      message
      npsSlabDetails
    }
  }
`;
export const RETRIEVE_NPS_CONFIGURATION_DETAILS = gql`
  query listNpsConfigurationDetails {
    listNpsConfigurationDetails {
      errorCode
      message
      npsConfigurationDetails
    }
  }
`;

export const RETRIEVE_EMPLOYEE_REGIME = gql`
  query getEmployeeTaxRegimeDetails(
    $employeeId: Int!
    $formId: Int!
    $requestResource: String
  ) {
    getEmployeeTaxRegimeDetails(
      employeeId: $employeeId
      formId: $formId
      requestResource: $requestResource
    ) {
      errorCode
      message
      taxRegimeDetails
    }
  }
`;
//mutation

export const UPDATE_NPS_FUND_RULES = gql`
  mutation updateHdmfRules(
    $npsRulesId: Int!
    $autoDeclaration: String!
    $autoDeclarationApplicableFor: String
    $investmentCategoryId: Int
    $npsDeductionPercentage: Float
    $enableEmployeeToDeclareEmployerShare: String
    $newRegimeMaxEmployerShare: Float
    $oldRegimeMaxEmployerShare: Float
    $newRegimeMaxEmployerShareAmount: Float
    $oldRegimeMaxEmployerShareAmount: Float
  ) {
    updateHdmfRules(
      npsRulesId: $npsRulesId
      autoDeclaration: $autoDeclaration
      autoDeclarationApplicableFor: $autoDeclarationApplicableFor
      investmentCategoryId: $investmentCategoryId
      npsDeductionPercentage: $npsDeductionPercentage
      enableEmployeeToDeclareEmployerShare: $enableEmployeeToDeclareEmployerShare
      newRegimeMaxEmployerShare: $newRegimeMaxEmployerShare
      oldRegimeMaxEmployerShare: $oldRegimeMaxEmployerShare
      newRegimeMaxEmployerShareAmount: $newRegimeMaxEmployerShareAmount
      oldRegimeMaxEmployerShareAmount: $oldRegimeMaxEmployerShareAmount
    ) {
      errorCode
      message
    }
  }
`;
export const UPDATE_NPS_CONFIGURATION_RULES = gql`
  mutation addUpdateHdmfConfiguration(
    $npsConfigurationId: Int!
    $calculationType: String!
    $employeeShareAmount: Float
    $employerShareAmount: Float
    $employerShare: Float
    $employeeShare: Float
    $includeInArrearsCalculation: String
  ) {
    addUpdateHdmfConfiguration(
      npsConfigurationId: $npsConfigurationId
      calculationType: $calculationType
      employeeShareAmount: $employeeShareAmount
      employerShareAmount: $employerShareAmount
      employerShare: $employerShare
      employeeShare: $employeeShare
      includeInArrearsCalculation: $includeInArrearsCalculation
    ) {
      errorCode
      message
    }
  }
`;
