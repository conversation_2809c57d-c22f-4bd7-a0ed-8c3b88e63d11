<template>
  <v-row>
    <v-col cols="12" class="d-flex justify-end mb-4">
      <v-btn
        v-if="props.formAccess?.update"
        variant="text"
        color="primary"
        density="compact"
        @click="emit('open-edit-form')"
      >
        <span class="mr-2">{{ t("common.update") }}</span>
        <v-icon size="13">fas fa-edit</v-icon>
      </v-btn>
    </v-col>
    <v-col cols="12">
      <v-row class="px-md-8">
        <v-col cols="12" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ t("settings.calculationType") }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ props.npsDetails[0].Retirals_Type }}
          </p>
        </v-col>
        <v-col
          v-if="
            props.npsDetails[0].Retirals_Type?.toLowerCase() == 'percentage'
          "
          cols="12"
          sm="6"
        >
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ t("settings.employerShare") }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ props.npsDetails[0].Employer_Share_Percentage || 0 }}
          </p>
        </v-col>
        <v-col v-else cols="12" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ t("settings.employerShareAmount") }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{ props.npsDetails[0].Employer_Share_Amount || 0 }}
          </p>
        </v-col>
        <v-col cols="12" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ t("settings.monthly") }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              props.npsDetails[0].Employer_Share_Amount
                ? props.npsDetails[0].Employer_Share_Amount.toFixed(2)
                : 0
            }}
          </p>
        </v-col>
        <v-col cols="12" sm="6">
          <p class="text-subtitle-1 text-grey-darken-1">
            {{ t("settings.annually") }}
          </p>
          <p class="text-subtitle-1 font-weight-regular">
            {{
              props.npsDetails[0].Employer_Share_Amount
                ? (props.npsDetails[0].Employer_Share_Amount * 12).toFixed(2)
                : 0
            }}
          </p>
        </v-col>
      </v-row>
    </v-col>
  </v-row>
</template>
<script setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const props = defineProps({
  landingFormName: {
    type: String,
    required: true,
  },
  formAccess: {
    type: [Boolean, Object],
    required: true,
  },
  npsDetails: {
    type: Array,
    required: true,
  },
});
const emit = defineEmits(["open-edit-form"]);
</script>
