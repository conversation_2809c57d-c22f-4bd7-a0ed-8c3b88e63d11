<template>
  <div style="height: 100%">
    <v-card class="pa-4 pb-1 team-availability-card rounded-lg">
      <!-- My Team Availability title -->
      <v-row>
        <v-col cols="12" class="py-2">
          <div class="d-flex align-center justify-space-between">
            <div class="d-flex align-center">
              <v-progress-circular
                model-value="100"
                color="blue-grey"
                :size="22"
                class="mr-2"
              />
              <span class="ml-2 text-primary text-h6 font-weight-bold">
                {{ $t("dashboard.myTeamAvailability") }}
              </span>
            </div>
          </div>
        </v-col>
      </v-row>

      <!-- Team Availability loading screen -->
      <v-row v-if="isTeamAvailabilityLoading" class="pa-1 pb-0">
        <v-col cols="12" class="py-0">
          <div class="team-availability-content">
            <v-row class="mb-3">
              <v-col cols="12">
                <v-skeleton-loader type="text@2" />
              </v-col>
            </v-row>
            <v-row v-for="index in 6" :key="index" class="mb-2">
              <v-col cols="2">
                <v-skeleton-loader type="avatar" />
              </v-col>
              <v-col cols="6">
                <v-skeleton-loader type="text" />
              </v-col>
              <v-col cols="4">
                <v-skeleton-loader type="chip" />
              </v-col>
            </v-row>
          </div>
        </v-col>
      </v-row>

      <!-- Team Availability Error And Empty screen -->
      <v-row
        v-else-if="errorInTeamAvailability || isNoTeamAvailabilityData"
        class="pa-1 pb-0"
      >
        <v-col cols="12" class="py-0">
          <div class="d-flex align-center team-availability-content">
            <NoDataCardWithQuotes
              id="admin_dashboard_team_availability_refresh"
              image-name="admin-dashboard/team-availability"
              :primary-bold-text="$t('dashboard.greatThings')"
              :text-message="$t('dashboard.areNeverDone')"
              :is-small-card="false"
              :card-type="errorInTeamAvailability ? 'error' : 'no-data'"
              :error-content="
                errorInTeamAvailability
                  ? $t('dashboard.technicalDifficulties')
                  : ''
              "
              image-size="80%"
              :is-show-image="windowWidth > 960"
              @refresh-triggered="refreshMyTeamAvailability()"
            />
          </div>
        </v-col>
      </v-row>

      <!-- Team Availability Content -->
      <v-row v-else class="pa-1 pb-0">
        <v-col cols="12" class="py-0 team-availability-content">
          <apexchart
            v-if="teamAvailabilitySeries.length > 0"
            type="line"
            height="290"
            :options="teamAvailabilityOptions"
            :series="teamAvailabilitySeries"
          />
        </v-col>
      </v-row>
    </v-card>
  </div>
</template>

<script>
import moment from "moment";
import VueApexCharts from "vue3-apexcharts";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";

export default {
  name: "MyTeamAvailability",

  components: {
    apexchart: VueApexCharts,
    NoDataCardWithQuotes,
  },

  data() {
    return {
      isTeamAvailabilityLoading: false,
      isNoTeamAvailabilityData: false,
      errorInTeamAvailability: false,
      teamAvailabilitySeries: [],
      teamAvailabilityOptions: {
        chart: {
          toolbar: {
            show: false,
          },
          type: "line",
          zoom: {
            enabled: false,
          },
        },
        dataLabels: {
          enabled: false,
        },
        stroke: {
          width: [4, 4],
          curve: "smooth",
        },
        legend: {
          show: true,
          horizontalAlign: "left",
          position: "top",
          onItemHover: {
            highlightDataSeries: true,
          },
          onItemClick: {
            toggleDataSeries: false,
          },
        },
        colors: ["#F44336", "#8BC34A"],
        markers: {
          size: 4,
          colors: "#fff",
          strokeColors: ["#F44336", "#8BC34A"],
          strokeWidth: 2,
          hover: {
            size: 7,
          },
        },
        xaxis: {
          categories: [],
        },
        yaxis: {
          title: {
            text: "Number of Employees",
          },
        },
        tooltip: {
          custom: function ({ series, dataPointIndex }) {
            const absentValue = series[0][dataPointIndex];
            const presentValue = series[1][dataPointIndex];

            return (
              '<div class="chart-custom-tooltip">' +
              '<div class="tooltip-item">' +
              '<span class="chart-tooltip-absent"></span>' +
              '<span class="chart-tooltip-content">' +
              absentValue +
              "</span>" +
              '<span class="chart-tooltip-label"> - Absent</span>' +
              "</div>" +
              '<div class="tooltip-item">' +
              '<span class="chart-tooltip-present"></span>' +
              '<span class="chart-tooltip-content">' +
              presentValue +
              "</span>" +
              '<span class="chart-tooltip-label"> - Present</span>' +
              "</div>" +
              "</div>"
            );
          },
        },
        grid: {
          borderColor: "#f1f1f1",
        },
      },
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    baseUrl() {
      // return "https://fieldforce.hrapp.co.in/";
      return this.$store.getters.baseUrl;
    },
    teamAvailabilityData() {
      return this.$store.state.dashboard.teamAvailability;
    },
    employeeAvailabilityCountArray() {
      let teamAvailability = this.teamAvailabilityData;
      let employeeAvailability = {
        absentArray: [],
        presentArray: [],
      };

      if (Array.isArray(teamAvailability) && teamAvailability.length > 0) {
        teamAvailability.forEach((team) => {
          // Ensure the values are numbers and handle null/undefined
          const absentCount = Number(team.absentCount) || 0;
          const presentCount = Number(team.presentCount) || 0;

          employeeAvailability.absentArray.push(absentCount);
          employeeAvailability.presentArray.push(presentCount);
        });
      }

      return employeeAvailability;
    },
    weekDateArray() {
      let teamAvailability = this.teamAvailabilityData;
      let weekDaysArray = [];

      if (Array.isArray(teamAvailability) && teamAvailability.length > 0) {
        teamAvailability.forEach((weekday) => {
          if (weekday.date) {
            let date = moment(weekday.date).format("DD MMM");
            weekDaysArray.push(date);
          }
        });
      }

      return weekDaysArray;
    },
  },

  mounted() {
    this.getMyTeamAvailability();
  },

  methods: {
    // Load team availability data
    async getMyTeamAvailability() {
      let vm = this;
      vm.isTeamAvailabilityLoading = true;
      try {
        const apiObj = {
          url:
            vm.baseUrl +
            "employees/attendance/get-employee-details/isAction/Team Availability",
          type: "PoST",
          dataType: "json",
          data: {
            requestResource: "HRAPPUI",
          },
        };
        const response = await this.$store.dispatch(
          "triggerControllerFunction",
          apiObj
        );
        if (response.success) {
          let teamAvailabilityData = response;
          let empAvailabilityArray =
            teamAvailabilityData.presentAbsentCountList;
          vm.$store.commit(
            "dashboard/UPDATE_TEAM_AVAILABILITY",
            empAvailabilityArray
          );
          if (empAvailabilityArray.length > 0) {
            vm.getTeamAvailabilityChart();
          } else {
            vm.isNoTeamAvailabilityData = true;
          }
        } else {
          if (response?.status == 200) {
            //we get this status 200 in case of session expired so redirect user to auth
            vm.userLogout();
          } else if (response?.msg === "Session Expired") {
            vm.userLogout();
          } else {
            /* To handle internal server error */
            vm.errorInTeamAvailability = true;
          }
        }
      } catch (error) {
        this.errorInTeamAvailability = true;
      } finally {
        this.isTeamAvailabilityLoading = false;
      }
    },
    getTeamAvailabilityChart() {
      let empAvailability = this.employeeAvailabilityCountArray;
      let weekDates = this.weekDateArray;

      // Ensure we have valid data before updating the chart
      if (
        !empAvailability ||
        !Array.isArray(empAvailability.absentArray) ||
        !Array.isArray(empAvailability.presentArray)
      ) {
        return;
      }

      if (!Array.isArray(weekDates) || weekDates.length === 0) {
        return;
      }

      // Update series data
      this.teamAvailabilitySeries = [
        {
          name: "Absent",
          data: empAvailability.absentArray,
        },
        {
          name: "Present",
          data: empAvailability.presentArray,
        },
      ];

      // Update chart options with new categories
      this.teamAvailabilityOptions = {
        ...this.teamAvailabilityOptions,
        xaxis: {
          ...this.teamAvailabilityOptions.xaxis,
          categories: weekDates,
        },
      };
    },
    //Function to show eror or success message inside dashboard
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    //incase of session expired error got from backend redirect to auth
    userLogout() {
      this.$store.dispatch("clearUserLock");
    },
    refreshMyTeamAvailability() {
      this.errorInTeamAvailability = false;
      this.getMyTeamAvailability();
    },

    // Get employee initials for avatar
    getEmployeeInitials(name) {
      if (!name) return "?";
      const names = name.split(" ");
      if (names.length >= 2) {
        return (names[0].charAt(0) + names[1].charAt(0)).toUpperCase();
      }
      return name.charAt(0).toUpperCase();
    },

    // Get status color
    getStatusColor(status) {
      const colorMap = {
        PRESENT: "green",
        ON_LEAVE: "orange",
        ABSENT: "red",
        LATE: "amber",
        WORK_FROM_HOME: "blue",
      };
      return colorMap[status] || "grey";
    },
  },
};
</script>

<style scoped>
.team-availability-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.team-availability-content {
  min-height: 200px;
  max-height: 300px;
}

.scroll-area {
  height: 100%;
}

.border-bottom {
  border-bottom: 1px solid rgba(0, 0, 0, 0.12);
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .team-availability-content {
    min-height: 150px;
    max-height: 500px;
  }
}
</style>

<style>
/* ApexChart Custom Tooltip Styles */
.chart-custom-tooltip {
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  padding: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  font-family: inherit;
  min-width: 120px;
}

.tooltip-header {
  font-weight: bold;
  font-size: 14px;
  color: #333;
  margin-bottom: 8px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 4px;
}

.tooltip-item {
  display: flex;
  align-items: center;
  margin: 4px 0;
  font-size: 13px;
}

.chart-tooltip-absent,
.chart-tooltip-present {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.chart-tooltip-absent {
  background-color: #f44336;
}

.chart-tooltip-present {
  background-color: #8bc34a;
}

.chart-tooltip-content {
  font-weight: bold;
  color: #333;
  margin-right: 4px;
}

.chart-tooltip-label {
  color: #666;
}
</style>
