import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const GET_USER_SIGNIN_DETAILS = gql`
  query getUserAuthDetails($refreshToken: String!) {
    getUserAuthDetails(refreshToken: $refreshToken) {
      errorCode
      message
      authDetails
    }
  }
`;

export const GET_ENTOMO_USER_DETAILS = gql`
  query getEntomoUserAuthDetails($userName: String!) {
    getEntomoUserAuthDetails(userName: $userName) {
      errorCode
      message
      authDetails
    }
  }
`;

// ===============
// Mutation
// ===============

export const OAUTH_TOKEN_REFRESH = gql`
  mutation oauthTokenRefresh($bearerToken: String!, $clientId: String!) {
    oauthTokenRefresh(bearerToken: $bearerToken, clientId: $clientId) {
      success
      message
      data {
        accessToken
        refreshToken
        accessTokenExpiresIn
        refreshTokenExpiresIn
      }
    }
  }
`;
