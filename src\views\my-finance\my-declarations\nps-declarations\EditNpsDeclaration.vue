<template>
  <v-form ref="npsDeclarationForm">
    <v-row>
      <v-col cols="12" class="d-flex justify-end mb-4">
        <div>
          <v-btn
            rounded="lg"
            variant="text"
            elevation="4"
            color="primary"
            @click="emit('close-edit-form')"
          >
            {{ t("common.cancel") }}
          </v-btn>
          <v-btn
            rounded="lg"
            class="ml-3"
            color="primary"
            @click="validateNpsDeclaration()"
          >
            {{ t("common.save") }}
          </v-btn>
        </div>
      </v-col>
      <v-col cols="12">
        <v-row class="px-md-8">
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ t("settings.calculationType") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{ props.npsDetails[0].Retirals_Type }}
            </p>
          </v-col>
          <v-col
            v-if="
              props.npsDetails[0].Retirals_Type?.toLowerCase() == 'percentage'
            "
            cols="12"
            sm="6"
          >
            <v-text-field
              :label="t('settings.employerShare')"
              v-model="employerSharePercentage"
              type="number"
              :min="0"
              :max="100"
              variant="solo"
              :rules="[
                minMaxNumberValidation(
                  t('settings.employerShare'),
                  employerSharePercentage,
                  0,
                  maxPercentage
                ),
                twoDecimalPrecisionValidation(employerSharePercentage),
              ]"
              style="max-width: 300px"
              @update:model-value="isFormDirty = true"
            ></v-text-field>
          </v-col>
          <v-col v-else cols="12" sm="6">
            <v-text-field
              :label="t('settings.employerShareAmount')"
              v-model="employerShareAmount"
              type="number"
              variant="solo"
              :rules="[
                minMaxNumberValidation(
                  t('settings.employerShareAmount'),
                  employerShareAmount,
                  0,
                  maxAmount
                ),
                noDecimalValidation(employerShareAmount),
              ]"
              style="max-width: 300px"
              @update:model-value="isFormDirty = true"
            ></v-text-field>
          </v-col>
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ t("settings.monthly") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                isFormDirty && employerSharePercentage
                  ? "System Calculated"
                  : monthlyAmount
                  ? parseFloat(monthlyAmount).toFixed(2)
                  : 0
              }}
            </p>
          </v-col>
          <v-col cols="12" sm="6">
            <p class="text-subtitle-1 text-grey-darken-1">
              {{ t("settings.annually") }}
            </p>
            <p class="text-subtitle-1 font-weight-regular">
              {{
                isFormDirty && employerSharePercentage
                  ? "System Calculated"
                  : monthlyAmount
                  ? (monthlyAmount * 12).toFixed(2)
                  : 0
              }}
            </p>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-form>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script setup>
import { onMounted, ref, getCurrentInstance, computed } from "vue";
import { useI18n } from "vue-i18n";
import { useStore } from "vuex";
import { useValidation } from "@/composables/validationComposables";
const { t } = useI18n();
const store = useStore();
const instance = getCurrentInstance();
const {
  minMaxNumberValidation,
  noDecimalValidation,
  twoDecimalPrecisionValidation,
} = useValidation();
const props = defineProps({
  landingFormName: {
    type: String,
    required: true,
  },
  npsDetails: {
    type: Array,
    required: true,
  },
  employeeId: {
    type: Number,
    required: true,
  },
  employeeSalaryDetails: {
    type: Object,
    required: true,
  },
});

const employerSharePercentage = ref(null);
const employerShareAmount = ref(null);
const monthlyAmount = ref(null);
const isFormDirty = ref(false);
onMounted(() => {
  if (props.npsDetails[0].Retirals_Type?.toLowerCase() == "percentage") {
    employerSharePercentage.value = parseFloat(
      props.npsDetails[0].Employer_Share_Percentage
    );
  } else {
    employerShareAmount.value = parseFloat(
      props.npsDetails[0].Employer_Share_Amount
    );
  }
  monthlyAmount.value = parseFloat(props.npsDetails[0].Employer_Share_Amount);
  fetchNpsRules();
  fetchEmployeeRegime();
});
const npsRulesData = ref(null);
import { RETRIEVE_NPS_FUND_RULES } from "@/graphql/tax-and-statutory-compliance/npsRules";
const fetchNpsRules = () => {
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_NPS_FUND_RULES,
      client: "apolloClientAI",
      fetchPolicy: "no-cache",
    })
    .then((response) => {
      if (response.data && response.data.listNpsRules) {
        let npsFundData = JSON.parse(
          response.data.listNpsRules.npsRulesDetails
        );
        npsRulesData.value = npsFundData[0];
      }
    })
    .catch((err) => {
      store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "NPS declaration",
        isListError: false,
      });
    });
};
const employeeRegime = ref(null);
import { RETRIEVE_EMPLOYEE_REGIME } from "@/graphql/tax-and-statutory-compliance/npsRules";
const fetchEmployeeRegime = () => {
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_EMPLOYEE_REGIME,
      client: "apolloClientAI",
      variables: {
        employeeId: props.employeeId,
        formId: 389,
      },
      fetchPolicy: "no-cache",
    })
    .then((response) => {
      if (response.data && response.data.getEmployeeTaxRegimeDetails) {
        let regimeDetails = JSON.parse(
          response.data.getEmployeeTaxRegimeDetails.taxRegimeDetails
        );
        employeeRegime.value = regimeDetails.taxRegime;
      }
    })
    .catch((err) => {
      store.dispatch("handleApiErrors", {
        error: err,
        action: "retrieving",
        form: "NPS declaration",
        isListError: false,
      });
    });
};
const maxPercentage = computed(() => {
  if (employeeRegime.value?.toLowerCase() == "new regime") {
    return npsRulesData.value.New_Regime_Max_Employer_Share || 0;
  } else {
    return npsRulesData.value.Old_Regime_Max_Employer_Share || 0;
  }
});
const maxAmount = computed(() => {
  if (employeeRegime.value?.toLowerCase() == "new regime") {
    return npsRulesData.value.New_Regime_Max_Employer_Share_Amount || 0;
  } else {
    return npsRulesData.value.Old_Regime_Max_Employer_Share_Amount || 0;
  }
});
const emit = defineEmits(["close-edit-form", "edit-updated"]);
const npsDeclarationForm = ref(null);
const validateNpsDeclaration = async () => {
  const { valid } = await npsDeclarationForm.value.validate();
  if (valid) {
    calculateSalary();
  }
};
import {
  ADD_UPDATE_SALARY_DETAILS,
  RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
} from "@/graphql/corehr/salaryQueries.js";
const isLoading = ref(false);
const saveNpsDeclaration = () => {
  isLoading.value = true;
  let allowances = [];
  if (
    props.employeeSalaryDetails.allowances &&
    Object.keys(props.employeeSalaryDetails.allowances).length
  ) {
    Object.keys(props.employeeSalaryDetails.allowances).forEach((key) => {
      props.employeeSalaryDetails.allowances[key]?.forEach((item) => {
        allowances.push({
          allowanceTypeId: item.Allowance_Type_Id,
          amount: item.Amount?.toString(),
          percentage: item.Percentage?.toString(),
          allowanceType: item.Allowance_Type,
          allowanceWages: item.AllowanceWages?.toString(),
        });
      });
    });
  }
  let retirals = [];
  if (
    props.employeeSalaryDetails.retirals &&
    props.employeeSalaryDetails.retirals.length
  ) {
    props.employeeSalaryDetails.retirals.forEach((item) => {
      retirals.push({
        formId: item.Form_Id?.toString(),
        retiralsId: item.Retirals_Id?.toString(),
        retiralsType: item.Retirals_Type?.toString(),
        employeeSharePercentage: item.Employee_Share_Percentage?.toString(),
        employerSharePercentage: item.Employer_Share_Percentage?.toString(),
        employeeShareAmount: item.Employee_Share_Amount?.toString(),
        employerShareAmount: item.Employer_Share_Amount?.toString(),
        pfEmployeeContribution: item.PF_Employee_Contribution?.toString(),
        pfEmployerContribution: item.PF_Employer_Contribution,
        employeeStatutoryLimit: item.Employee_Statutory_Limit,
        employerStatutoryLimit: item.Employer_Statutory_Limit,
        employeeRetiralWages: item.Employee_Retiral_Wages?.toString(),
        employerRetiralWages: item.Employer_Retiral_Wages?.toString(),
        eligibleForEPS: item.Eligible_for_EPS || 0,
        adminCharge: item.Admin_Charge?.toString(),
        edliCharge: item.EDLI_Charge?.toString(),
      });
    });
  }
  instance.proxy.$apollo
    .mutate({
      mutation: ADD_UPDATE_SALARY_DETAILS,
      variables: {
        formId: 207,
        accessFormId: 389,
        isEditMode: true,
        employeeId: props.employeeId,
        templateId: props.employeeSalaryDetails.Template_Id,
        annualCTC: props.employeeSalaryDetails.Annual_CTC?.toString(),
        effectiveFrom: props.employeeSalaryDetails.Effective_From,
        effectiveTo: props.employeeSalaryDetails.Effective_To,
        monthlyGrossSalary:
          props.employeeSalaryDetails.Monthly_Gross_Salary?.toString(),
        annualGrossSalary:
          props.employeeSalaryDetails.Annual_Gross_Salary?.toString(),
        allowance: allowances,
        retirals: retirals,
      },
      client: "apolloClientF",
    })
    .then(() => {
      isLoading.value = false;
      let snackbarData = {
        isOpen: true,
        message: "NPS declaration updated successfully.",
        type: "success",
      };
      store.commit("OPEN_SNACKBAR", snackbarData);
      emit("edit-updated");
    })
    .catch((err) => {
      isLoading.value = false;
      store.dispatch("handleApiErrors", {
        error: err,
        action: "updating",
        form: "NPS declaration",
        isListError: false,
      });
    });
};
const providentFundConfig = computed(() => {
  return store.state.orgDetails.providentFundConfiguration;
});
const calculateSalary = () => {
  isLoading.value = true;
  let salaryDetails = {
    Employee_Salary_Id: props.employeeSalaryDetails.Employee_Salary_Id,
    Employee_Id: props.employeeId,
    Annual_Ctc: props.employeeSalaryDetails.Annual_CTC,
    Basic_Pay: props.employeeSalaryDetails.Basic_Pay,
    Effective_From: props.employeeSalaryDetails.Effective_From,
    Effective_To: props.employeeSalaryDetails.Effective_To,
    ESI_Contribution_End_Date: null,
    Status: "Active",
  };
  let allowances = [];
  if (
    props.employeeSalaryDetails.allowances &&
    Object.keys(props.employeeSalaryDetails.allowances).length
  ) {
    Object.keys(props.employeeSalaryDetails.allowances).forEach((key) => {
      props.employeeSalaryDetails.allowances[key]?.forEach((item) => {
        allowances.push({
          Allowance_Type_Id: item.Allowance_Type_Id,
          Amount: item.Amount?.toString(),
          Percentage: item.Percentage?.toString(),
          Allowance_Type: item.Allowance_Type,
        });
      });
    });
  }
  let retirals = [];
  if (
    props.employeeSalaryDetails.retirals &&
    props.employeeSalaryDetails.retirals.length
  ) {
    props.employeeSalaryDetails.retirals.forEach((item) => {
      retirals.push({
        Form_Id: item.Form_Id,
        Retirals_Id: item.Retirals_Id,
        Retirals_Type: item.Retirals_Type,
        Employee_Share_Percentage: item.Employee_Share_Percentage,
        Employer_Share_Percentage:
          item.Form_Id === 126
            ? employerSharePercentage.value
            : item.Employer_Share_Percentage,
        Employee_Share_Amount: item.Employee_Share_Amount,
        Employer_Share_Amount:
          item.Form_Id === 126
            ? employerShareAmount.value
            : item.Employer_Share_Amount,
        PF_Employee_Contribution: item.PF_Employee_Contribution,
        PF_Employer_Contribution: item.PF_Employer_Contribution,
        Employee_Statutory_Limit: item.Employee_Statutory_Limit,
        Employer_Statutory_Limit: item.Employer_Statutory_Limit,
        Eligible_for_EPS: item.Eligible_for_EPS,
        Admin_Charge: item.Admin_Charge,
        EDLI_Charge: item.EDLI_Charge,
      });
    });
  }
  instance.proxy.$apollo
    .query({
      query: RETRIEVE_SYSTEM_CALCULATED_COMPONENTS,
      client: "apolloClientAT",
      variables: {
        employeeId: props.employeeId,
        retiralDetails: JSON.stringify(retirals),
        allowanceDetails: JSON.stringify(allowances),
        salaryDetails: JSON.stringify(salaryDetails),
        providentFundConfigurationValue: providentFundConfig.value,
      },
      fetchPolicy: "no-cache",
    })
    .then(({ data }) => {
      if (
        data?.calculateSalary?.employeeRetiralDetails &&
        !data.calculateSalary.errorCode
      ) {
        let salaryComponents = JSON.parse(
          data?.calculateSalary?.employeeRetiralDetails
        );
        if (salaryComponents?.employeeSalaryAllowance?.length) {
          salaryComponents.employeeSalaryAllowance.forEach((item) => {
            let keysToUse = [
              "basicPayArray",
              "fixedAllowanceArray",
              "allowanceArray",
              "flexiBenefitPlanArray",
              "reimbursementArray",
            ];
            keysToUse.forEach((key) => {
              if (props.employeeSalaryDetails.allowances[key].length) {
                props.employeeSalaryDetails.allowances[key].forEach(
                  (allowanceItem) => {
                    if (
                      parseInt(item.Allowance_Type_Id) ===
                      parseInt(allowanceItem.Allowance_Type_Id)
                    ) {
                      allowanceItem.Amount = item.Amount || 0;
                    }
                  }
                );
              }
            });
          });
        }
        if (salaryComponents?.employeeSalaryRetirals?.length) {
          const matchedKeys = new Set(
            salaryComponents.employeeSalaryRetirals.map(
              (item) => `${item.Retirals_Id}|${item.Form_Id}`
            )
          );
          props.employeeSalaryDetails.retirals.map((retiralItem) => {
            const key = `${retiralItem.Retirals_Id}|${retiralItem.Form_Id}`;
            const match = matchedKeys.has(key);

            if (match) {
              const matchedItem = salaryComponents.employeeSalaryRetirals.find(
                (item) =>
                  parseInt(item.Form_Id) === parseInt(retiralItem.Form_Id) &&
                  parseInt(item.Retirals_Id) ===
                    parseInt(retiralItem.Retirals_Id)
              );

              if (matchedItem) {
                retiralItem.Employee_Share_Amount =
                  matchedItem.Employee_Share_Amount;
                retiralItem.Employer_Share_Amount =
                  matchedItem.Employer_Share_Amount;
                retiralItem.Amount = matchedItem.Employer_Share_Amount;
                retiralItem.Employer_Share_Percentage =
                  matchedItem.Employer_Share_Percentage;
                retiralItem.Employee_Share_Percentage =
                  matchedItem.Employee_Share_Percentage;
                if (retiralItem.Form_Id === 126) {
                  monthlyAmount.value = matchedItem.Employer_Share_Amount;
                  employerShareAmount.value = matchedItem.Employer_Share_Amount;
                }
              }
            }
          });
        }
        if (salaryComponents?.employeeSalaryBonus?.length) {
          salaryComponents.employeeSalaryBonus.forEach((item) => {
            props.employeeSalaryDetails.allowances.bonusArray?.forEach(
              (bonusItem) => {
                if (
                  parseInt(item.Allowance_Type_Id) ===
                  parseInt(bonusItem.Allowance_Type_Id)
                ) {
                  bonusItem.Amount = item.Amount;
                  bonusItem.Percentage = item.Percentage;
                }
              }
            );
          });
        }
        isFormDirty.value = false;
        saveNpsDeclaration();
      }
    })
    .catch((err) => {
      isLoading.value = false;
      store.dispatch("handleApiErrors", {
        error: err,
        action: "calculating",
        form: "NPS declaration",
        isListError: false,
      });
    });
};
</script>
