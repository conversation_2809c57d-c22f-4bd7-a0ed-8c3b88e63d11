<template>
  <div class="ma-5">
    <div v-if="listLoading" class="mt-3">
      <v-skeleton-loader
        ref="skeleton1"
        type="table-heading"
        class="mx-auto"
      ></v-skeleton-loader>
      <div v-for="i in 3" :key="i" class="mt-4">
        <v-skeleton-loader
          ref="skeleton2"
          type="list-item-avatar"
          class="mx-auto"
        ></v-skeleton-loader>
      </div>
    </div>
    <AppFetchErrorScreen
      v-else-if="isErrorInList"
      :content="errorContent"
      icon-name="fas fa-redo-alt"
      :button-text="showRetryBtn ? 'Retry' : ''"
      @button-click="refetchData()"
    >
    </AppFetchErrorScreen>
    <AppFetchErrorScreen
      v-else-if="mainList.length === 0 && !openAddForm"
      key="no-results-screen"
    >
      <template #contentSlot>
        <div style="max-width: 80%" class="mx-auto">
          <v-row
            v-if="!isLoading"
            style="background: white"
            class="rounded-lg pa-5 mb-4"
            :class="isMobileView ? 'mt-n16' : ''"
          >
            <v-col cols="12">
              <NotesCard
                :notes="note1"
                backgroundColor="transparent"
                class="mb-4"
              ></NotesCard>
              <NotesCard
                :notes="note2"
                backgroundColor="transparent"
                class="mb-2"
              ></NotesCard>
            </v-col>
            <v-col cols="12" class="d-flex align-center justify-center mb-4">
              <v-btn
                v-if="formAccess.add"
                variant="elevated"
                color="primary"
                class="ml-4 mt-1"
                rounded="lg"
                :size="isMobileView ? 'small' : 'default'"
                @click="onAdd()"
              >
                <v-icon size="15" class="pr-1">fas fa-plus</v-icon>
                Add
              </v-btn>
              <v-btn
                color="white"
                rounded="lg"
                class="ml-2 mt-1"
                :size="isMobileView ? 'small' : 'default'"
                @click="refetchData()"
              >
                <v-icon>fas fa-redo-alt</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </div>
      </template>
    </AppFetchErrorScreen>
    <div v-else>
      <v-row v-if="openViewForm">
        <v-col cols="12" :class="windowWidth < 900 ? 'mt-4' : 'mt-2'">
          <v-btn rounded="lg" color="primary" @click="openViewForm = false">
            <span><i class="fa fa-chevron-left pr-2"></i> Back</span></v-btn
          >
        </v-col>
        <v-col cols="12">
          <ViewInsuranceType
            :editFormData="insuranceTypeData"
            @open-edit="openEditForm()"
            :accessFormName="accessFormName"
            :getFieldAlias="labelList"
            :selectedItem="selectedItem"
            :slabList="slabList"
            :formAccess="formAccess"
          ></ViewInsuranceType>
        </v-col>
      </v-row>
      <v-row v-else-if="openAddForm">
        <v-col cols="12">
          <AddEditInsuranceType
            :selectedItem="selectedItem"
            :items="mainList"
            :accessFormName="accessFormName"
            :getFieldAlias="labelList"
            :isEdit="isEdit"
            @close-form="closeAllForms"
            @refetch-data="refetchData"
          >
          </AddEditInsuranceType>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col cols="12" class="mt-6">
          <ListInsuranceType
            :items="mainList"
            :getFieldAlias="labelList"
            :formAccess="formAccess"
            :accessFormName="accessFormName"
            @open-view-form="onOpenViewForm($event)"
            @refetch-data="refetchData"
            @open-add-form="onAdd"
          ></ListInsuranceType>
        </v-col>
      </v-row>
    </div>
  </div>
  <AppLoading v-if="isLoading"></AppLoading>
</template>
<script>
import { defineAsyncComponent } from "vue";
const NotesCard = defineAsyncComponent(() =>
  import("@/components/helper-components/NotesCard")
);
const AddEditInsuranceType = defineAsyncComponent(() =>
  import("./AddEditInsuranceType.vue")
);
// components
import ViewInsuranceType from "./ViewInsuranceType.vue";

import ListInsuranceType from "./ListInsuranceType.vue";
// Queries
import { RETRIEVE_INSURANCE_RULES } from "@/graphql/tax-and-statutory-compliance/insuranceType";
export default {
  name: "InsuranceType",
  components: {
    ViewInsuranceType,
    NotesCard,
    ListInsuranceType,
    AddEditInsuranceType,
  },
  data() {
    return {
      isLoading: false,
      listLoading: false,
      isErrorInList: false,
      errorContent: "",
      showRetryBtn: true,
      insuranceTypeData: {},
      isEdit: false,
      mainList: [],
      slabList: [],
      openViewForm: false,
      selectedItem: {},
      openAddForm: false,
    };
  },
  computed: {
    note1() {
      return `We prioritize flexibility and customization to ensure fair compensation for employees' dedication. A standout feature is the ability to create personalized insurance policies. In the insurance configuration module, administrators can easily design unique plans, set contribution levels, specify percentages and amounts, and tailor settings for specific departments or roles. This adaptability ensures that insurance offerings precisely match employees' diverse needs, contributing to a comprehensive and individualized benefits package.`;
    },
    note2() {
      return `Insurance configuration empowers organizations to create customized insurance policies catering to both employers and employees. This robust system allows fine-tuning of parameters like insurance details, contribution levels, percentages, amounts, declaration preferences, and payment frequency. With this comprehensive insurance setup, businesses can effectively manage financial risks, offer valuable benefits to their workforce, and ensure a resilient and well-protected team.`;
    },
    formAccess() {
      let fAccess = this.accessRights("378");
      if (fAccess && fAccess.accessRights && fAccess.accessRights["view"]) {
        return fAccess.accessRights;
      } else return false;
    },
    accessFormName() {
      let fAccess = this.accessRights("378");
      if (fAccess && fAccess.customFormName) {
        return fAccess.customFormName;
      } else return "Insurance Types";
    },
    accessRights() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    labelList() {
      return this.$store.state.customFormFields;
    },
  },

  errorCaptured(err, vm, info) {
    let url = window.location.href;
    console.error("Insurance Error:", err);
    let msg = `Something went wrong while loading the ${this.accessFormName.toLowerCase()} form. Please try after some time.`;
    if (this.orgCode === "capricecloud" || url.includes("hrapp.co.in")) {
      msg = err + " " + info;
    }
    let snackbarData = {
      isOpen: true,
      message: msg,
      type: "warning",
    };
    this.showAlert(snackbarData);
    return false;
  },

  mounted() {
    this.fetchInsuranceTypeDetails();
  },
  methods: {
    openEditForm() {
      this.isEdit = true;
      this.openViewForm = false;
      this.openAddForm = true;
    },
    closeAllForms() {
      this.isEdit = false;
      this.openAddForm = false;
      this.openViewForm = false;
    },
    onAdd() {
      this.selectedItem = null;
      this.openAddForm = true;
      this.isEdit = false;
    },
    refetchData() {
      this.errorContent = "";
      this.isErrorInList = false;
      this.closeAllForms();
      this.fetchInsuranceTypeDetails();
    },
    onOpenViewForm(item) {
      this.selectedItem = item;
      this.openViewForm = true;
    },
    fetchInsuranceTypeDetails() {
      let vm = this;
      vm.listLoading = true;
      vm.$apollo
        .query({
          query: RETRIEVE_INSURANCE_RULES,
          client: "apolloClientAI",
          fetchPolicy: "no-cache",
        })
        .then((response) => {
          if (response.data && response.data.retrieveInsuranceRules) {
            let mainList = JSON.parse(
              response.data.retrieveInsuranceRules.insuranceRulesData
            );
            vm.mainList = mainList;
            let slabList = response.data.retrieveInsuranceRules.slabWiseData;
            if (slabList && slabList.length) {
              vm.slabList = JSON.parse(slabList);
            }

            vm.listLoading = false;
          } else {
            // Form Name will be going to change dynamically
            vm.handleListError((err = ""), this.accessFormName);
          }
        })
        .catch((err) => {
          // Form Name will be going to change dynamically
          vm.handleListError(err, this.accessFormName);
        });
    },
    handleListError(err = "", formName) {
      this.listLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: formName,
          isListError: true,
        })
        .then((errorMessages) => {
          this.errorContent = errorMessages;
          this.isErrorInList = true;
        });
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>
