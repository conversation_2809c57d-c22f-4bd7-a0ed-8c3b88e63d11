<template>
  <div>
    <div v-if="slabList.length > 0 && tableHeaders.length > 0">
      <div
        class="py-6 rounded-lg ma-3 pa-3 card-height bg-grey-lighten-5"
        :class="isMobileView ? '' : 'px-3'"
      >
        <v-row class="ma-6">
          <v-col :cols="12" class="mb-12 mt-6">
            <v-data-table
              :headers="tableHeaders"
              :items="slabList"
              fixed-header
              :items-per-page="slabList.length"
              :items-per-page-options="[
                { value: 50, title: '50' },
                { value: 100, title: '100' },
                { value: -1, title: '$vuetify.dataFooter.itemsPerPageAll' },
              ]"
              :height="
                $store.getters.getTableHeightBasedOnScreenSize(200, slabList)
              "
              style="box-shadow: none !important"
              class="elevation-1"
            >
              <template v-slot:item="{ item }">
                <tr
                  class="data-table-tr bg-white cursor-pointer"
                  :class="
                    isMobileView ? ' v-data-table__mobile-table-row mt-2' : ''
                  "
                >
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                    v-if="getFieldAlias[57].Field_Visiblity == 'Yes'"
                  >
                    <div
                      v-if="isMobileView"
                      class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                    >
                      {{ getFieldAlias[57].Field_Alias }}
                    </div>
                    <section>
                      <span class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(item.Range_From) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                    v-if="getFieldAlias[58].Field_Visiblity == 'Yes'"
                  >
                    <div
                      v-if="isMobileView"
                      class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                    >
                      {{ getFieldAlias[58].Field_Alias }}
                    </div>
                    <section>
                      <span class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(item.Range_To) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                    v-if="getFieldAlias[60].Field_Visiblity == 'Yes'"
                  >
                    <div
                      v-if="isMobileView"
                      class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                    >
                      {{ getFieldAlias[60].Field_Alias }}
                    </div>
                    <section>
                      <span class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(item.Capped_Value) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                    v-if="getFieldAlias[61].Field_Visiblity == 'Yes'"
                  >
                    <div
                      v-if="isMobileView"
                      class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                    >
                      {{ getFieldAlias[61].Field_Alias + " " }} (in %)
                    </div>
                    <section>
                      <span class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(item.Employee_Share) }}
                      </span>
                    </section>
                  </td>
                  <td
                    :class="
                      isMobileView
                        ? 'd-flex justify-space-between align-center'
                        : 'pa-2 pl-5'
                    "
                  >
                    <div
                      v-if="isMobileView"
                      class="d-flex align-center text-subtitle-1 text-grey-darken-1"
                    >
                      {{ getFieldAlias[62].Field_Alias + " " }} (in %)
                    </div>
                    <section>
                      <span class="text-subtitle-1 font-weight-regular">
                        {{ checkNullValue(item.Employer_Share) }}
                      </span>
                    </section>
                  </td>
                </tr>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
    </div>
  </div>
</template>
<script>
import { checkNullValue } from "@/helper.js";
export default {
  name: "ListPFSlabList",
  props: {
    slabList: {
      type: [Array, Object],
      required: true,
      default: () => {},
    },
    getFieldAlias: {
      type: [Array, Object],
      default: () => {
        return {};
      },
    },
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    // current window size
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    tableHeaders() {
      let headers = [];
      if (this.getFieldAlias[57].Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[57].Field_Alias,
          key: this.getFieldAlias[57].Field_Alias,
          sortable: false,
        });
      }
      if (this.getFieldAlias[58].Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[58].Field_Alias,
          key: this.getFieldAlias[58].Field_Alias,
          sortable: false,
        });
      }
      if (this.getFieldAlias[60].Field_Visiblity == "Yes") {
        headers.push({
          title: "Maximum Limit",
          key: this.getFieldAlias[60].Field_Alias,
          sortable: false,
        });
      }
      if (this.getFieldAlias[61].Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[61].Field_Alias + " (in %)",
          key: this.getFieldAlias[61].Field_Alias,
          sortable: false,
        });
      }
      if (this.getFieldAlias[62].Field_Visiblity == "Yes") {
        headers.push({
          title: this.getFieldAlias[62].Field_Alias + " (in %)",
          key: this.getFieldAlias[62].Field_Alias,
          sortable: false,
        });
      }
      return headers;
    },
  },
  methods: {
    checkNullValue,
  },
};
</script>
