const prod_baseurl = "https://api.hrapp.co/";
const stag_baseurl = "https://api.hrapp.co.in/";
const onboarding_stag_baseurl = "https://onboardapi.hrapp.co.in/";
const onboarding_prod_baseurl = "https://onboardapi.hrapp.co/";
const irukka_integration_stag_baseurl =
  "https://aju0i48d0f.execute-api.ap-south-1.amazonaws.com/uat/partner/api/v1/partnerIntegration";
const irukka_integration_prod_baseurl = "/";
const seek_baseurl = "https://graphql.seek.com/";
const microsoft_login_url = "https://login.microsoftonline.com/common";

const production = {
  domain: "hrapp.co",
  productLogo: 0,
  production: 1,
  TZDate: "m/d/Y",
  graphql_endpoint: {
    stepFunction: prod_baseurl + "rographql",
    ats: prod_baseurl + "ats/graphql",
    atsExternal: prod_baseurl + "ats/external",
    atswographl: prod_baseurl + "ats/wographql",
    atsrographl: prod_baseurl + "ats/rographql",
    atsSignIn: prod_baseurl + "ats/signinGraphql",
    settings: prod_baseurl + "hrappBe/settingsgraphql",
    hrappBE: prod_baseurl + "hrappBe/graphql",
    employeeMonitoring: prod_baseurl + "employeeMonitoring/graphql",
    empMonitorRead: prod_baseurl + "employeeMonitoring/roGraphql",
    payMaster: prod_baseurl + "paymaster/graphql",
    payMasterRead: prod_baseurl + "paymaster/rographql",
    billing: prod_baseurl + "billing/billinggraphql",
    coreHrRead: prod_baseurl + "coreHr/rographql",
    coreHrWrite: prod_baseurl + "coreHr/wographql ",
    coreHrNoAuth: prod_baseurl + "coreHr/noauthrographql",
    coreHrExternal: prod_baseurl + "coreHr/external",
    docusignRead: prod_baseurl + "docusign/rographql",
    docusignWrite: prod_baseurl + "docusign/wographql ",
    docusignNoAuthRead: prod_baseurl + "docusign/noauthrographql",
    docusignNoAuthWrite: prod_baseurl + "docusign/noauthwographql",
    hrappBERead: prod_baseurl + "hrappBe/roGraphql",
    hrappBEWrite: prod_baseurl + "hrappBe/woGraphql",
    empMonitorWrite: prod_baseurl + "employeeMonitoring/woGraphql",
    attendanceRead: prod_baseurl + "facialAttendance/rographql",
    attendanceWrite: prod_baseurl + "facialAttendance/wographql",
    onboardingRead: onboarding_prod_baseurl + "regraphql",
    onboardingReadNoAuth: onboarding_prod_baseurl + "rographql",
    onboardingWriteNoAuth: onboarding_prod_baseurl + "wographql",
    onboardingWrite: onboarding_prod_baseurl + "wegraphql",
    onboardingReadWrite: onboarding_prod_baseurl + "graphql",
    exitManagement: prod_baseurl + "exitManagement/exitManagement",
    payrollAdminRead: prod_baseurl + "payrollAdmin/roGraphql",
    payrollAdminWrite: prod_baseurl + "payrollAdmin/woGraphql",
    empSelfServiceRead: prod_baseurl + "employeeSelfService/rographql",
    empSelfServiceWrite: prod_baseurl + "employeeSelfService/wographql",
    settingsRead: prod_baseurl + "hrappBe/settingsroGraphql",
    settingsWrite: prod_baseurl + "hrappBe/settingswoGraphql",
    integrationRead: prod_baseurl + "integration/rographql",
    integrationWrite: prod_baseurl + "integration/wographql",
    taxAndStatutory: prod_baseurl + "employee-taxation/graphql",
    taxAndStatutoryRead: prod_baseurl + "taxAndStatutory/rographql",
    taxAndStatutoryWrite: prod_baseurl + "taxAndStatutory/wographql",
    dynamicFormBuilder:
      prod_baseurl + "dynamicFormBuilder/graphqldynamicformbuilder",
    batchProcessingRead: prod_baseurl + "batchProcessing/rographql",
    batchProcessingWrite: prod_baseurl + "batchProcessing/wographql",
    batchProcessingroauthgraphql:
      prod_baseurl + "batchProcessing/roauthgraphql",
    batchProcessingExternal: prod_baseurl + "batchProcessing/external",
    orgChart: prod_baseurl + "orgChart/orgchart",
    seekEndpoint: seek_baseurl + "graphql",
    orgDataRead: prod_baseurl + "orgDataManagement/rographql",
    orgDataWrite: prod_baseurl + "orgDataManagement/wographql",
    entomoTokenRefresh: prod_baseurl + "oauth/tokens",
  },
  workflowUrl: prod_baseurl + "workflowEngine",
  irukkaUrl: irukka_integration_prod_baseurl,
  microsoftLogin: microsoft_login_url,
  hrappWebhookApiUrlForIndeed:
    prod_baseurl + "integration/indeedWebHookEndpoint",
  indeedScreeningQuestions: prod_baseurl + "integration/indeedApplyQuestions",
  jobPostUrlForIndeed: "hrapp.co",
  firebase_credentials: {
    apiKey: "AIzaSyAupi9_2ATYi05M7hfgO3pZFqF1dNGK7tk",
    authDomain: "hrappidentity.firebaseapp.com",
    databaseURL: "https://hrappidentity.firebaseio.com",
    projectId: "hrappidentity",
    storageBucket: "hrappidentity.appspot.com",
    messagingSenderId: "887685568909",
  },
  // need to change prod config here
  tl_firebase_credentials: {
    apiKey: "AIzaSyCUfF3bLGc3yU_qBK91Tx72_3HaWf3cTYc",
    authDomain: "hrapptruleadidentity.firebaseapp.com",
    databaseURL:
      "https://hrapptruleadidentity-default-rtdb.asia-southeast1.firebasedatabase.app",
    projectId: "hrapptruleadidentity",
    storageBucket: "hrapptruleadidentity.appspot.com",
    messagingSenderId: "265861830197",
  },
  ipAddressApi: "https://api.ipify.org/?format=json",
  publicImageS3Path: "https://s3.ap-south-1.amazonaws.com/s3.logos.hrapp.co/",
  googleMapsAPIKey: "AIzaSyA8X1UihjoJuukpRgUr4QFy-R6PrnTiXyw",
  googleFontsAPIKey: "AIzaSyBN3v3bTbNx6FDmkHBj6g_drdhSHuzR_WI",
  googleFontsAPIUrl: "https://www.googleapis.com/webfonts/v1/webfonts",
  mixPanelToken: "6df21e89c6a0f6b1bc345ae98b6ef36e",
};
const staging = {
  domain: "hrapp.co.in",
  productLogo: 1,
  production: 1,
  TZDate: "m/d/Y",
  graphql_endpoint: {
    stepFunction: stag_baseurl + "rographql",
    ats: stag_baseurl + "ats/graphql",
    atsExternal: stag_baseurl + "ats/external",
    atswographl: stag_baseurl + "ats/wographql",
    atsrographl: stag_baseurl + "ats/rographql",
    atsSignIn: stag_baseurl + "ats/signinGraphql",
    settings: stag_baseurl + "hrappBe/settingsgraphql",
    hrappBE: stag_baseurl + "hrappBe/graphql",
    employeeMonitoring: stag_baseurl + "employeeMonitoring/graphql",
    empMonitorRead: stag_baseurl + "employeeMonitoring/roGraphql",
    payMaster: stag_baseurl + "paymaster/graphql",
    payMasterRead: stag_baseurl + "paymaster/rographql",
    billing: stag_baseurl + "billing/billinggraphql",
    coreHrRead: stag_baseurl + "coreHr/rographql",
    coreHrWrite: stag_baseurl + "coreHr/wographql",
    coreHrNoAuth: stag_baseurl + "coreHr/noauthrographql",
    coreHrExternal: stag_baseurl + "coreHr/external",
    docusignRead: stag_baseurl + "docusign/rographql",
    docusignWrite: stag_baseurl + "docusign/wographql ",
    docusignNoAuthRead: stag_baseurl + "docusign/noauthrographql",
    docusignNoAuthWrite: stag_baseurl + "docusign/noauthwographql",
    hrappBERead: stag_baseurl + "hrappBe/roGraphql",
    hrappBEWrite: stag_baseurl + "hrappBe/woGraphql",
    empMonitorWrite: stag_baseurl + "employeeMonitoring/woGraphql",
    attendanceRead: stag_baseurl + "facialAttendance/rographql",
    attendanceWrite: stag_baseurl + "facialAttendance/wographql",
    onboardingRead: onboarding_stag_baseurl + "regraphql",
    onboardingReadNoAuth: onboarding_stag_baseurl + "rographql",
    onboardingWriteNoAuth: onboarding_stag_baseurl + "wographql",
    onboardingWrite: onboarding_stag_baseurl + "wegraphql",
    onboardingReadWrite: onboarding_stag_baseurl + "graphql",
    exitManagement: stag_baseurl + "exitManagement/exitManagement",
    payrollAdminRead: stag_baseurl + "payrollAdmin/roGraphql",
    payrollAdminWrite: stag_baseurl + "payrollAdmin/woGraphql",
    empSelfServiceRead: stag_baseurl + "employeeSelfService/rographql",
    empSelfServiceWrite: stag_baseurl + "employeeSelfService/wographql",
    settingsRead: stag_baseurl + "hrappBe/settingsroGraphql",
    settingsWrite: stag_baseurl + "hrappBe/settingswoGraphql",
    integrationRead: stag_baseurl + "integration/rographql",
    integrationWrite: stag_baseurl + "integration/wographql",
    taxAndStatutory: stag_baseurl + "employee-taxation/graphql",
    taxAndStatutoryRead: stag_baseurl + "taxAndStatutory/rographql",
    taxAndStatutoryWrite: stag_baseurl + "taxAndStatutory/wographql",
    dynamicFormBuilder:
      stag_baseurl + "dynamicFormBuilder/graphqldynamicformbuilder",
    batchProcessingRead: stag_baseurl + "batchProcessing/rographql",
    batchProcessingWrite: stag_baseurl + "batchProcessing/wographql",
    batchProcessingroauthgraphql:
      stag_baseurl + "batchProcessing/roauthgraphql",
    batchProcessingExternal: stag_baseurl + "batchProcessing/external",
    orgChart: stag_baseurl + "orgChart/orgchart",
    seekEndpoint: seek_baseurl + "graphql",
    orgDataRead: stag_baseurl + "orgDataManagement/rographql",
    orgDataWrite: stag_baseurl + "orgDataManagement/wographql",
    entomoTokenRefresh: stag_baseurl + "oauth/tokens",
  },
  workflowUrl: stag_baseurl + "workflowEngine",
  irukkaUrl: irukka_integration_stag_baseurl,
  microsoftLogin: microsoft_login_url,
  hrappWebhookApiUrlForIndeed:
    stag_baseurl + "integration/indeedWebHookEndpoint",
  indeedScreeningQuestions: stag_baseurl + "integration/indeedApplyQuestions",
  jobPostUrlForIndeed: "hrapp.co.in",
  firebase_credentials: {
    apiKey: "AIzaSyB-QCDxis2HG3hHIreLPiidSlN_eCyi3m8",
    authDomain: "hrappsample.firebaseapp.com",
    databaseURL: "https://hrappsample.firebaseio.com",
    projectId: "hrappsample",
    storageBucket: "hrappsample.appspot.com",
    messagingSenderId: "514098503657",
  },
  tl_firebase_credentials: {
    apiKey: "AIzaSyDqZn6gno9dipDFBl4RSRBgCVX0-rb3BaU",
    authDomain: "hrapptrulead.firebaseapp.com",
    databaseURL: "https://hrapptrulead-default-rtdb.firebaseio.com",
    projectId: "hrapptrulead",
    storageBucket: "hrapptrulead.appspot.com",
    messagingSenderId: "805458700336",
  },
  ipAddressApi: "https://api.ipify.org/?format=json",
  publicImageS3Path:
    "https://s3.ap-south-1.amazonaws.com/s3.hrapp-dev-public-images/",
  googleMapsAPIKey: "AIzaSyA8X1UihjoJuukpRgUr4QFy-R6PrnTiXyw",
  googleFontsAPIKey: "AIzaSyBN3v3bTbNx6FDmkHBj6g_drdhSHuzR_WI",
  googleFontsAPIUrl: "https://www.googleapis.com/webfonts/v1/webfonts",
  mixPanelToken: "6df21e89c6a0f6b1bc345ae98b6ef36e",
};

const configuration =
  process.env.NODE_ENV === "production" ? production : staging;

export default {
  // Add common config values here
  MAX_ATTACHMENT_SIZE: 5000000,
  ...configuration,
};
