<template>
  <div class="fill-height">
    <v-card class="card-highlight rounded-lg d-flex flex-column" height="100%">
      <!-- Header Section -->
      <v-card-title class="d-flex align-center pa-4 pb-2">
        <RingWaveAnimation
          v-if="!loadingContent && !errorInActions && actionsListLength > 0"
          class="mr-3"
          :display-text="actionsListLength"
        />
        <v-progress-circular
          v-else
          model-value="100"
          color="blue-accent-2"
          :size="22"
          class="mr-3"
        />
        <span class="text-h6 text-primary font-weight-bold">
          {{ $t("dashboard.myActions") }}
        </span>
      </v-card-title>

      <!-- Loading State -->
      <v-card-text v-if="loadingContent">
        <v-skeleton-loader
          v-for="i in 3"
          :key="i"
          class="mx-auto mb-2"
          type="list-item-avatar-two-line"
        />
      </v-card-text>

      <!-- Error or Empty State -->
      <v-card-text
        v-else-if="errorInActions || actionsListLength === 0"
        class="pa-4 d-flex align-center justify-center"
        style="min-height: 15rem"
      >
        <NoDataCardWithQuotes
          image-name="dashboard/actions-empty-image"
          :primary-bold-text="$t('dashboard.lifeIsPercent')"
          :text-message="$t('dashboard.whatHappensToYou')"
          :bottom-bold-text="$t('dashboard.andPercent')"
          :bottom-text-message="$t('dashboard.howYouReactToIt')"
          :is-small-card="false"
          image-size="70%"
          :is-show-image="windowWidth > 960"
          :card-type="errorInActions ? 'error' : 'no-data'"
          :error-content="
            errorInActions ? $t('dashboard.technicalDifficulties') : ''
          "
          @refresh-triggered="fnRefreshActions()"
        />
      </v-card-text>

      <!-- Actions Content -->
      <v-card-text v-else class="pa-4 d-flex flex-column flex-grow-1">
        <perfect-scrollbar
          class="w-100 overflow-y-auto overflow-x-hidden actions-scrollbar"
        >
          <div>
            <!-- Missed Out Attendance for full day -->
            <PresentActionCards
              v-for="(missedAttendance, index) in missedAttendanceList"
              :key="'missed-attendance-' + index"
              :card-property="fnMakeRandomColors()"
              :title="formatAttendanceDate(missedAttendance.absent_date)"
              :list-index="index"
              :is-clickable="isFormHasViewRights('Attendance')"
              @action-triggered="employeeFormRedirectionUrl('Attendance')"
            >
              <template #avatarContent>
                <div
                  class="d-flex flex-column pa-2 bg-green-lighten-4"
                  style="min-height: 40px; min-width: 40px; border-radius: 15px"
                >
                  <v-avatar size="24" color="green">
                    <v-icon color="white" size="18" class="font-weight-bold">
                      fas fa-power-off
                    </v-icon>
                  </v-avatar>
                </div>
              </template>
              <template #subTitleContent>
                <div v-if="missedAttendance.iscurrentdate === 1">
                  {{ $t("dashboard.youMissed") }}
                  <span class="text-success">{{
                    $t("dashboard.checkIn")
                  }}</span>
                </div>
                <div v-else>
                  {{ $t("dashboard.youMissed") }}
                  <span class="text-success">{{
                    $t("dashboard.checkIn")
                  }}</span>
                  {{ $t("dashboard.and") }}
                  <span class="text-error">{{ $t("dashboard.checkOut") }}</span>
                </div>
              </template>
              <template v-if="addAttendanceRights" #actionContent>
                <v-tooltip
                  v-if="
                    checkDateIsBetweenSalaryDates(missedAttendance.absent_date)
                  "
                  :text="$t('dashboard.attendanceCannotBeRegularized')"
                  location="top"
                  max-width="300"
                >
                  <template v-slot:activator="{ props }">
                    <v-btn
                      color="grey-lighten-3"
                      size="small"
                      v-bind="props"
                      class="mt-2 cursor-not-allow"
                    >
                      <span class="text-primary">{{
                        $t("dashboard.add")
                      }}</span>
                    </v-btn>
                  </template>
                </v-tooltip>
                <v-btn
                  v-else
                  color="#ffe4d5"
                  size="small"
                  rounded="lg"
                  class="mt-2"
                  @click.stop="openNoAttendanceForm(missedAttendance)"
                >
                  <span class="text-primary">{{ $t("dashboard.add") }}</span>
                </v-btn>
              </template>
            </PresentActionCards>

            <!-- Missed Out Check out Attendance -->
            <PresentActionCards
              v-for="(attendance, index) in missedCheckoutAttendanceList"
              :key="'attendance-' + index"
              :card-property="fnMakeRandomColors()"
              :title="formatAttendanceDate(attendance.regular_form)"
              :list-index="index"
              :is-clickable="isFormHasViewRights('Attendance')"
              @action-triggered="employeeFormRedirectionUrl('Attendance')"
            >
              <template #avatarContent>
                <div
                  class="d-flex flex-column pa-2 bg-red-lighten-4"
                  style="min-height: 40px; min-width: 40px; border-radius: 15px"
                >
                  <v-avatar size="24" color="red">
                    <v-icon color="white" size="18" class="font-weight-bold">
                      fas fa-power-off
                    </v-icon>
                  </v-avatar>
                </div>
              </template>
              <template #subTitleContent>
                {{ $t("dashboard.youMissed") }}
                <span class="text-error">{{ $t("dashboard.checkOut") }}</span>
              </template>
              <template v-if="updateAttendanceRights" #actionContent>
                <v-btn
                  v-if="checkDateIsBetweenSalaryDates(attendance.regular_form)"
                  color="grey-lighten-3"
                  size="small"
                  disabled
                  class="mt-2"
                >
                  <span class="text-primary">{{ $t("dashboard.add") }}</span>
                </v-btn>
                <v-btn
                  v-else
                  color="#ffe4d5"
                  size="small"
                  class="mt-2"
                  @click.stop="openMissedAttendanceForm(attendance)"
                >
                  <span class="text-primary">{{ $t("dashboard.add") }}</span>
                </v-btn>
              </template>
            </PresentActionCards>

            <!-- Leaves -->
            <PresentActionCards
              v-for="(leavesList, a) in filteredLeavesList"
              :key="a + '-leaves'"
              :card-property="fnMakeRandomColors()"
              :title="leavesList.leaveName"
              :sub-title-bold="
                leavesList.totalDays + ' ' + $t('dashboard.days')
              "
              :sub-title-text="
                leavesList.startDate && leavesList.endDate
                  ? leavesList.startDate + ' - ' + leavesList.endDate
                  : leavesList.startDate
                  ? leavesList.startDate
                  : leavesList.endDate
              "
              :list-index="a"
              :is-clickable="isFormHasViewRights('Leaves')"
              icon-name="far fa-clock"
              @action-triggered="employeeFormRedirectionUrl('Leaves')"
            >
              <template #actionContent>
                <CommentBox
                  :status="leavesList.approvalStatus"
                  :comments="leavesList.comments"
                  icon-name="fas fa-comment"
                />
              </template>
            </PresentActionCards>

            <!-- Short Time Off -->
            <PresentActionCards
              v-for="(shortOffList, b) in filteredShortOffList"
              :key="b + '-shortoff'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Employees', 'Short Time Off')"
              :sub-title-bold="
                shortOffList.totalHours + ' ' + $t('dashboard.hours')
              "
              :sub-title-text="fnFormShortTimeOffDate(shortOffList.startDate)"
              :list-index="b"
              :is-clickable="isFormHasViewRights('Short Time Off')"
              icon-name="far fa-clock"
              @action-triggered="employeeFormRedirectionUrl('Short Time Off')"
            >
              <template #actionContent>
                <CommentBox
                  :status="shortOffList.approvalStatus"
                  :comments="shortOffList.comments"
                  icon-name="fas fa-comment"
                />
              </template>
            </PresentActionCards>

            <!-- Comp Off -->
            <PresentActionCards
              v-for="(compOffList, c) in filteredCompOffList"
              :key="c + '-compoff'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Employees', 'Compensatory Off')"
              :sub-title-bold="
                compOffList.totalDays + ' ' + $t('dashboard.days')
              "
              :sub-title-text="compOffList.compensatoryDate"
              :list-index="c"
              :is-clickable="isFormHasViewRights('Compensatory Off')"
              icon-name="far fa-clock"
              @action-triggered="employeeFormRedirectionUrl('Compensatory Off')"
            >
              <template #actionContent>
                <CommentBox
                  :status="compOffList.approvalStatus"
                  :comments="compOffList.comments"
                  icon-name="fas fa-comment"
                />
              </template>
            </PresentActionCards>

            <!-- attendance -->
            <PresentActionCards
              v-for="(attendanceItem, d) in attendanceList"
              :key="d + '-attendance'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Employees', attendanceItem.formName)"
              sub-title-bold="Applied Date "
              :sub-title-text="attendanceItem.addedOn"
              :list-index="d"
              :is-clickable="isFormHasViewRights(attendanceItem.formName)"
              icon-name="fas fa-user-clock"
              @action-triggered="
                employeeFormRedirectionUrl(attendanceItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="attendanceItem.status"
                  :comments="attendanceItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- employee travel -->
            <PresentActionCards
              v-for="(travelItem, e) in empTravelList"
              :key="e + '-employee-travel'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Employees', travelItem.formName)"
              sub-title-bold="Applied Date "
              :sub-title-text="travelItem.addedOn"
              :list-index="e"
              :is-clickable="isFormHasViewRights(travelItem.formName)"
              icon-name="fas fa-car-side"
              @action-triggered="
                employeeFormRedirectionUrl(travelItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="travelItem.status"
                  :comments="travelItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- performance assessment -->
            <PresentActionCards
              v-for="(performanceItem, f) in performAssessList"
              :key="f + '-performanceAssess'"
              :card-property="fnMakeRandomColors()"
              :title="
                fnGetCustomFormName('Employees', performanceItem.formName)
              "
              sub-title-bold="Applied Date "
              :sub-title-text="performanceItem.addedOn"
              :list-index="f"
              :is-clickable="isFormHasViewRights(performanceItem.formName)"
              icon-name="fas fa-chart-line"
              @action-triggered="
                employeeFormRedirectionUrl(performanceItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="performanceItem.status"
                  :comments="performanceItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- time sheet -->
            <PresentActionCards
              v-for="(timesheetItem, g) in timeSheetList"
              :key="g + '-timesheets'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Employees', timesheetItem.formName)"
              sub-title-bold="Applied Date "
              :sub-title-text="timesheetItem.addedOn"
              :list-index="g"
              :is-clickable="isFormHasViewRights(timesheetItem.formName)"
              icon-name="far fa-calendar-alt"
              @action-triggered="
                employeeFormRedirectionUrl(timesheetItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="timesheetItem.status"
                  :comments="timesheetItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- transfer -->
            <PresentActionCards
              v-for="(transferItem, h) in transferList"
              :key="h + '-transfer'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Employees', transferItem.formName)"
              sub-title-bold="Applied Date "
              :sub-title-text="transferItem.addedOn"
              :list-index="h"
              :is-clickable="isFormHasViewRights(transferItem.formName)"
              icon-name="fas fa-exchange-alt"
              @action-triggered="
                employeeFormRedirectionUrl(transferItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="transferItem.status"
                  :comments="transferItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- resignation -->
            <PresentActionCards
              v-for="(resignationItem, index) in resignationList"
              :key="index + '-resignation'"
              :card-property="fnMakeRandomColors()"
              :title="
                fnGetCustomFormName('Employees', resignationItem.formName)
              "
              sub-title-bold="Applied Date "
              :sub-title-text="resignationItem.addedOn"
              :list-index="index"
              :is-clickable="isFormHasViewRights(resignationItem.formName)"
              icon-name="fas fa-user-minus"
              @action-triggered="
                employeeFormRedirectionUrl(resignationItem.formName)
              "
            >
              <template #actionContent>
                <span
                  class="text-purple text-lighten-1 font-weight-bold pr-3 text-body-2"
                  >{{ resignationItem.status }}</span
                >
              </template>
            </PresentActionCards>

            <!-- advance salary -->
            <PresentActionCards
              v-for="(advanceSalItem, i) in advanceSalaryList"
              :key="i + '-advance-salary'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', advanceSalItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(advanceSalItem.amount)"
              :sub-title-text="'Applied Date: ' + advanceSalItem.addedOn"
              :list-index="i"
              :is-clickable="isFormHasViewRights(advanceSalItem.formName)"
              icon-name="fas fa-money-check-alt"
              @action-triggered="
                payrollFormRedirectionUrl(advanceSalItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="advanceSalItem.status"
                  :comments="advanceSalItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- bonus -->
            <PresentActionCards
              v-for="(bonusItem, j) in bonusList"
              :key="j + '-bonus'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', bonusItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(bonusItem.amount)"
              :sub-title-text="'Applied Date: ' + bonusItem.addedOn"
              :list-index="j"
              :is-clickable="isFormHasViewRights(bonusItem.formName)"
              icon-name="fas fa-money-bill-wave"
              @action-triggered="payrollFormRedirectionUrl(bonusItem.formName)"
            >
              <template #actionContent>
                <CommentBox
                  :status="bonusItem.status"
                  :comments="bonusItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- commission -->
            <PresentActionCards
              v-for="(commissionItem, k) in commissionList"
              :key="k + '-commission'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', commissionItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(commissionItem.amount)"
              :sub-title-text="'Applied Date: ' + commissionItem.addedOn"
              :list-index="k"
              :is-clickable="isFormHasViewRights(commissionItem.formName)"
              icon-name="fas fa-hand-holding-usd"
              @action-triggered="
                payrollFormRedirectionUrl(commissionItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="commissionItem.status"
                  :comments="commissionItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- deduction -->
            <PresentActionCards
              v-for="(deductionItem, l) in deductionList"
              :key="l + '-deduction'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', deductionItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(deductionItem.amount)"
              :sub-title-text="'Applied Date: ' + deductionItem.addedOn"
              :list-index="l"
              :is-clickable="isFormHasViewRights(deductionItem.formName)"
              icon-name="fas fa-cut"
              @action-triggered="
                payrollFormRedirectionUrl(deductionItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="deductionItem.status"
                  :comments="deductionItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- loan -->
            <PresentActionCards
              v-for="(loanItem, m) in loanList"
              :key="m + '-loan'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', loanItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(loanItem.amount)"
              :sub-title-text="'Applied Date: ' + loanItem.addedOn"
              :list-index="m"
              :is-clickable="isFormHasViewRights(loanItem.formName)"
              icon-name="fas fa-landmark"
              @action-triggered="payrollFormRedirectionUrl(loanItem.formName)"
            >
              <template #actionContent>
                <CommentBox
                  :status="loanItem.status"
                  :comments="loanItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- deferred loan -->
            <PresentActionCards
              v-for="(deferredLoanItem, n) in deferredLoanList"
              :key="n + '-def-loan'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', deferredLoanItem.formName)"
              sub-title-bold=""
              :sub-title-text="'Applied Date: ' + deferredLoanItem.addedOn"
              :list-index="n"
              :is-clickable="isFormHasViewRights(deferredLoanItem.formName)"
              icon-name="fas fa-landmark"
              @action-triggered="
                payrollFormRedirectionUrl(deferredLoanItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="deferredLoanItem.status"
                  :comments="deferredLoanItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- reimbursement -->
            <PresentActionCards
              v-for="(reimbursementItem, o) in reimbursementList"
              :key="o + '-reimbursement'"
              :card-property="fnMakeRandomColors()"
              :title="
                fnGetCustomFormName('Payroll', reimbursementItem.formName)
              "
              :sub-title-bold="fnDisplayAmountValue(reimbursementItem.amount)"
              :sub-title-text="'Applied Date: ' + reimbursementItem.addedOn"
              :list-index="o"
              :is-clickable="isFormHasViewRights(reimbursementItem.formName)"
              icon-name="far fa-credit-card"
              @action-triggered="
                payrollFormRedirectionUrl(reimbursementItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="reimbursementItem.status"
                  :comments="reimbursementItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- rented property -->
            <!-- rented don't have separate access so passing main form name -->
            <PresentActionCards
              v-for="(rentedItem, p) in rentedList"
              :key="p + '-rented'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', rentedItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(rentedItem.amount)"
              :sub-title-text="'Applied Date: ' + rentedItem.addedOn"
              :list-index="p"
              :is-clickable="isFormHasViewRights('Income Under Section24')"
              icon-name="fas fa-home"
              @action-triggered="
                payrollFormRedirectionUrl('Income Under Section24')
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="rentedItem.status"
                  :comments="rentedItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- self occupied property -->
            <!-- self occupied don't have separate access so passing main form name -->
            <PresentActionCards
              v-for="(selfOccupiedItem, q) in selfOccupiedList"
              :key="q + '-self-occupied'"
              :card-property="fnMakeRandomColors()"
              :title="fnGetCustomFormName('Payroll', selfOccupiedItem.formName)"
              :sub-title-bold="fnDisplayAmountValue(selfOccupiedItem.amount)"
              :sub-title-text="'Applied Date: ' + selfOccupiedItem.addedOn"
              :list-index="q"
              :is-clickable="isFormHasViewRights('Income Under Section24')"
              icon-name="fas fa-home"
              @action-triggered="
                payrollFormRedirectionUrl('Income Under Section24')
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="selfOccupiedItem.status"
                  :comments="selfOccupiedItem.comments"
                />
              </template>
            </PresentActionCards>

            <!-- shift allowance -->
            <PresentActionCards
              v-for="(shiftAllowanceItem, r) in shiftAllowanceList"
              :key="r + '-shit-allowance'"
              :card-property="fnMakeRandomColors()"
              :title="
                fnGetCustomFormName('Payroll', shiftAllowanceItem.formName)
              "
              :sub-title-bold="fnDisplayAmountValue(shiftAllowanceItem.amount)"
              :sub-title-text="'Applied Date: ' + shiftAllowanceItem.addedOn"
              :list-index="r"
              :is-clickable="isFormHasViewRights(shiftAllowanceItem.formName)"
              icon-name="fas fa-money-bill"
              @action-triggered="
                payrollFormRedirectionUrl(shiftAllowanceItem.formName)
              "
            >
              <template #actionContent>
                <CommentBox
                  :status="shiftAllowanceItem.status"
                  :comments="shiftAllowanceItem.comments"
                />
              </template>
            </PresentActionCards>
          </div>
        </perfect-scrollbar>
      </v-card-text>
    </v-card>

    <!-- Missed Out Attendance Form Modal -->
    <MissedOutAttendanceForm
      v-if="isOpenAttendanceModal"
      :open-attendance-entry-modal="isOpenAttendanceModal"
      :attendance-type="attendanceType"
      :attendance-data="missedAttendanceData"
      :wfh-pre-approval-error-message="wfhRegularizationPreApprovalErrorMessage"
      :failed-pre-approvals="failedPreApprovals"
      @close-attendance-modal="closeAttendanceModalForm"
    />
    <AppLoading v-if="validateWFHLoading" />
  </div>
</template>

<script>
import moment from "moment";
// Custom components
import { defineAsyncComponent } from "vue";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes.vue";
import PresentActionCards from "@/components/helper-components/PresentActionCards.vue";
import CommentBox from "@/components/helper-components/CommentBox.vue";
import RingWaveAnimation from "@/components/helper-components/RingWaveAnimation.vue";
const MissedOutAttendanceForm = defineAsyncComponent(() =>
  import("./MissedOutAttendanceForm.vue")
);
// Queries
import {
  LIST_PAYROLL_ACTIONS,
  LIST_EMPLOYEES_ACTIONS,
  VALIDATE_WFH_PREAPPORVAL_REQUEST,
} from "@/graphql/dashboard/dashboardQueries";

import {
  getErrorCodesAndMessagesWithValidation,
  handleNetworkErrors,
} from "@/helper";
export default {
  name: "MyActions",

  components: {
    NoDataCardWithQuotes,
    PresentActionCards,
    CommentBox,
    MissedOutAttendanceForm,
    RingWaveAnimation,
  },

  props: {
    randomColors: {
      type: Array,
      required: true,
    },
  },

  data() {
    return {
      openTimeConfigureModal: false,
      errorInEmpActions: false,
      errorInPayrollActions: false,
      isOpenAttendanceModal: false,
      missedAttendanceData: "",
      wfhRegularizationPreApprovalErrorMessage: "",
      attendanceType: "",
      failedPreApprovals: [],
      listEmployeeActionsLoading: false,
      listPayrollActionsLoading: false,
      validateWFHLoading: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    // Base url of the app
    baseUrl() {
      return this.$store.getters.baseUrl;
    },
    // Return form details
    formDetails() {
      return this.$store.getters.formAccessRights;
    },
    // List all form under Employees modules
    employeeFormsDetails() {
      return this.$store.getters.redirectionFormUrl("Employees");
    },
    // List all form under Payroll modules
    payrollFormsDetails() {
      return this.$store.getters.redirectionFormUrl("Payroll");
    },
    // Filter leave based on return status
    filteredLeavesList() {
      let levDetails = this.$store.state.dashboard.leavesLists || [];
      let leavesLists =
        levDetails.filter(
          (list) => list.approvalStatus?.toLowerCase() === "returned"
        ) || [];
      return leavesLists;
    },
    // Filter short time off based on return status
    filteredShortOffList() {
      let shortOffDetails = this.$store.state.dashboard.shortTimeOffLists || [];
      let shortOffLists =
        shortOffDetails.filter(
          (list) => list.approvalStatus?.toLowerCase() === "returned"
        ) || [];
      return shortOffLists;
    },
    // Filter comp off based on return status
    filteredCompOffList() {
      let compoffDetails = this.$store.state.dashboard.compOffLists || [];
      let compOffLists =
        compoffDetails.filter(
          (list) => list.approvalStatus?.toLowerCase() === "returned"
        ) || [];
      return compOffLists;
    },
    // Get all employee actions list
    employeesActionsList() {
      return this.$store.state.dashboard.employeesActionsList || {};
    },
    attendanceList() {
      let attendanceItem = this.employeesActionsList.attendance;
      return attendanceItem ? attendanceItem : [];
    },
    empTravelList() {
      let travelItem = this.employeesActionsList.employeeTravel;
      return travelItem ? travelItem : [];
    },
    performAssessList() {
      let performanceItem = this.employeesActionsList.performanceAssesment;
      return performanceItem ? performanceItem : [];
    },
    timeSheetList() {
      let timesheetItem = this.employeesActionsList.timesheets;
      return timesheetItem ? timesheetItem : [];
    },
    transferList() {
      let transferItem = this.employeesActionsList.transfer;
      return transferItem ? transferItem : [];
    },
    resignationList() {
      let resignationItem = this.employeesActionsList.resignation;
      return resignationItem ? resignationItem : [];
    },
    // Get all payroll actions list
    payrollActionsLists() {
      return this.$store.state.dashboard.payrollActionsList || {};
    },
    advanceSalaryList() {
      let advanceSalItem = this.payrollActionsLists?.advanceSalary;
      return advanceSalItem ? advanceSalItem : [];
    },
    bonusList() {
      let bonusItem = this.payrollActionsLists?.bonus;
      return bonusItem ? bonusItem : [];
    },
    commissionList() {
      let commissionItem = this.payrollActionsLists?.commission;
      return commissionItem ? commissionItem : [];
    },
    deductionList() {
      let deductionItem = this.payrollActionsLists?.deduction;
      return deductionItem ? deductionItem : [];
    },
    loanList() {
      let loanItem = this.payrollActionsLists?.loan;
      return loanItem ? loanItem : [];
    },
    deferredLoanList() {
      let deferredLoanItem = this.payrollActionsLists?.deferredLoan;
      return deferredLoanItem ? deferredLoanItem : [];
    },
    reimbursementList() {
      let reimbursementItem = this.payrollActionsLists?.reimbursement;
      return reimbursementItem ? reimbursementItem : [];
    },
    rentedList() {
      let rentedItem = this.payrollActionsLists?.rentedProperty;
      return rentedItem ? rentedItem : [];
    },
    selfOccupiedList() {
      let selfOccupiedItem = this.payrollActionsLists?.selfOccupiedProperty;
      return selfOccupiedItem ? selfOccupiedItem : [];
    },
    shiftAllowanceList() {
      let sftAllow = this.payrollActionsLists?.shiftAllowance;
      return sftAllow ? sftAllow : [];
    },
    // function to calculate the whole actions list
    actionsListLength() {
      // leaves are retrieved from timeoff components. So here we pick only returned status
      let leavesLength =
        this.filteredLeavesList.length +
        this.filteredShortOffList.length +
        this.filteredCompOffList.length;
      // total count of employee and payroll actions
      let actionsList =
        this.attendanceList.length +
        this.empTravelList.length +
        this.performAssessList.length +
        this.timeSheetList.length +
        this.transferList.length +
        this.resignationList.length +
        this.advanceSalaryList.length +
        this.bonusList.length +
        this.commissionList.length +
        this.deductionList.length +
        this.loanList.length +
        this.deferredLoanList.length +
        this.reimbursementList.length +
        this.rentedList.length +
        this.selfOccupiedList.length +
        this.shiftAllowanceList.length;

      //add count of no attendance and missed check out data
      let missedAttendanceList =
        this.missedAttendanceList.length +
        this.missedCheckoutAttendanceList.length;

      return leavesLength + actionsList + missedAttendanceList; // sum the actions count with leaves count
    },
    // To check actions error
    errorInActions() {
      if (
        this.errorInEmpActions &&
        this.errorInPayrollActions &&
        this.actionsListLength === 0 // checked because we may get leaves count(retrieved in timeoff component)
      )
        return true;
      // only when, both actions list gets error and the actionsList is zero
      else return false;
    },
    missedAttendanceLoading() {
      let noAttendanceLoading =
        this.$store.state.dashboard.isNoAttendanceLoading;
      let missedAttendanceLoading =
        this.$store.state.dashboard.isMissedAttendanceLoading;
      return noAttendanceLoading || missedAttendanceLoading;
    },
    loadingContent() {
      if (
        this.listEmployeeActionsLoading &&
        this.listPayrollActionsLoading &&
        this.missedAttendanceLoading &&
        this.actionsListLength === 0 // checked because we may get leaves count(retrieved in timeoff component)
      )
        return true;
      // only when, both actions list gets loaded and the actionsList is zero
      else return false;
    },
    missedCheckoutAttendanceList() {
      return this.$store.state.dashboard.missedCheckOutAttendance;
    },
    regularizationRequestLimitDetails() {
      return this.$store.state.dashboard.regularizationRequestLimitDetails;
    },
    missedAttendanceList() {
      return this.$store.state.dashboard.missedNoAttendance;
    },
    //check access rights for attendance-regularization
    attendanceRegularizationRights() {
      let attendanceRegularization = this.$store.getters.formAccessRights(
        "attendance-regularization"
      );
      if (attendanceRegularization && attendanceRegularization.accessRights) {
        return attendanceRegularization.accessRights;
      } else {
        return "";
      }
    },
    //get update rights for missed check out attendance
    updateAttendanceRights() {
      let updateAttendance = this.attendanceRegularizationRights;

      if (updateAttendance && updateAttendance.update) {
        return true;
      } else {
        return false;
      }
    },
    //get view rights for retrieve missed attendances
    viewAttendanceRights() {
      let viewAttendance = this.attendanceRegularizationRights;

      if (viewAttendance && viewAttendance.view) {
        return true;
      } else {
        return false;
      }
    },
    //get add rights for no attendance
    addAttendanceRights() {
      let addAttendance = this.attendanceRegularizationRights;

      if (addAttendance && addAttendance.add) {
        return true;
      } else {
        return false;
      }
    },
    loginEmployeeId() {
      return parseInt(this.$store.state.orgDetails.employeeId, 10);
    },
  },
  mounted() {
    this.fetchListEmployeeActions();
    this.fetchListPayrollActions();
    if (this.viewAttendanceRights) {
      this.retrieveMissedAttendanceData();
    }
  },

  methods: {
    fetchListEmployeeActions() {
      let vm = this;
      vm.listEmployeeActionsLoading = true;
      vm.errorInEmpActions = false;
      vm.$apollo
        .query({
          query: LIST_EMPLOYEES_ACTIONS,
          client: "apolloClientC",
        })
        .then(({ data }) => {
          // check data is empty or not (when error, data is returned as empty)
          if (data && Object.keys(data).length !== 0) {
            let empActionsData = data.listEmployeeActions;
            if (empActionsData && empActionsData.employeeActionsList) {
              let responseData = JSON.parse(empActionsData.employeeActionsList);
              let parsedData = responseData.employeeActionList;
              this.$store.commit(
                "dashboard/UPDATE_EMPLOYEES_ACTIONS_LIST",
                parsedData
              );
            }
          } else {
            // if any error occurs, data is returned empty object
            vm.errorInEmpActions = true;
          }
          vm.listEmployeeActionsLoading = false;
        })
        .catch(() => {
          vm.listEmployeeActionsLoading = false;
          vm.errorInEmpActions = true;
        });
    },
    fetchListPayrollActions() {
      let vm = this;
      vm.listPayrollActionsLoading = true;
      vm.errorInPayrollActions = false;
      vm.$apollo
        .query({
          query: LIST_PAYROLL_ACTIONS,
          client: "apolloClientC",
        })
        .then(({ data }) => {
          // check data is empty or not (when error, data is returned as empty)
          if (data && Object.keys(data).length !== 0) {
            let payrollActionsData = data.listPayrollActions;
            if (payrollActionsData && payrollActionsData.payrollActionsList) {
              let responseData = JSON.parse(
                payrollActionsData.payrollActionsList
              );
              let parsedData = responseData.payrollActionList;
              vm.$store.commit(
                "dashboard/UPDATE_PAYROLL_ACTIONS_LIST",
                parsedData
              );
            }
          } else {
            // if any error occurs, data is returned empty object
            vm.errorInPayrollActions = true;
          }
          vm.listPayrollActionsLoading = false;
        })
        .catch(() => {
          vm.listPayrollActionsLoading = false;
          vm.errorInPayrollActions = true;
        });
    },
    retrieveMissedAttendanceData() {
      //call action to retrieve no attendance entry by current logged employee for a month
      this.$store.dispatch("dashboard/fetchNoAttendanceRecord");

      //call action to retrieve missed check out attendance entry by current logged employee
      this.$store.dispatch("dashboard/fetchMissedCheckOutAttendance");
    },
    // Function to form random colors for actions card background, icon color and avatar background
    fnMakeRandomColors() {
      let finalColor =
        this.randomColors[Math.floor(Math.random() * this.randomColors.length)];
      return {
        background: "#F9FBFC", // Same background for all actions card
        bg: finalColor + "-lighten-4", // Combine vuetify base color with lighten-4 property
        color: finalColor, // Set vuetify base color as icon color
      };
    },
    // function to form start date and end date
    fnFormShortTimeOffDate(startDate) {
      if (startDate) {
        // date format is 01 Apr 2020 12:00 but we need to split date alone for short timeoff
        let startPeriod = startDate.split(" "); // returns ["01", "Apr", "2020", "12:00"]
        return startPeriod[0] + " " + startPeriod[1] + " " + startPeriod[2]; // join first three array params
      }
    },
    //formatAttendanceDate
    formatAttendanceDate(value) {
      let formattedDate = moment(value).format("D MMM yyyy");
      return formattedDate;
    },
    checkDateIsBetweenSalaryDates(date) {
      let regularizationRequestLimitArray =
        this.regularizationRequestLimitDetails;
      if (
        regularizationRequestLimitArray &&
        regularizationRequestLimitArray.length > 0
      ) {
        let inputDate = moment(date);
        let isExist = false;
        if (regularizationRequestLimitArray.length === 1) {
          const { lastSalaryDate, salaryDate } =
            regularizationRequestLimitArray[0]; // first array
          isExist =
            moment(inputDate).isBetween(salaryDate, lastSalaryDate) ||
            moment(inputDate).isSame(salaryDate) ||
            moment(inputDate).isSame(lastSalaryDate);
        }
        if (regularizationRequestLimitArray.length === 2) {
          const { lastSalaryDate, salaryDate } =
            regularizationRequestLimitArray[0]; // first array
          isExist =
            moment(inputDate).isBetween(salaryDate, lastSalaryDate) ||
            moment(inputDate).isSame(salaryDate) ||
            moment(inputDate).isSame(lastSalaryDate);
          // if date not exist in first array then check it in second array
          if (!isExist) {
            const { lastSalaryDate, salaryDate } =
              regularizationRequestLimitArray[1]; // second array
            isExist =
              moment(inputDate).isBetween(salaryDate, lastSalaryDate) ||
              moment(inputDate).isSame(salaryDate) ||
              moment(inputDate).isSame(lastSalaryDate);
          }
        }
        return isExist;
      }
      return false;
    },

    //open full attendance form
    openNoAttendanceForm(missedAttendance) {
      this.attendanceType = "no-attendance";
      let attendanceDate = missedAttendance.absent_date;
      this.validateWfhPreApprovalRequest(attendanceDate, missedAttendance);
    },

    //open missed check out form attendance
    openMissedAttendanceForm(missedAttendance) {
      this.attendanceType = "missed-attendance";
      let attendanceDate = missedAttendance.regular_form;
      this.validateWfhPreApprovalRequest(attendanceDate, missedAttendance);
    },
    // Close attendance modal
    closeAttendanceModal() {
      this.isOpenAttendanceModal = false;
      this.missedAttendanceData = "";
      this.attendanceType = "";
      this.failedPreApprovals = [];
    },
    // Refresh actions
    fnRefreshActions() {
      this.errorInEmpActions = false;
      this.errorInPayrollActions = false;
      this.fetchListEmployeeActions();
      this.fetchListPayrollActions();
      //incase of error refresh missed attendance
      if (this.viewAttendanceRights) {
        this.retrieveMissedAttendanceData();
      }
    },
    // function to get custom form name (for displayed as actions card heading)
    fnGetCustomFormName(moduleName, formName) {
      // formName has space and capitalized. So we have to add - instead of space and convert name lo lowercase(ex: short-time-off)
      let formattedFormName = formName
        ?.trim()
        .replace(/\s/g, "-")
        .toLowerCase();
      let formProperty = this.formDetails(formattedFormName);
      return formProperty ? formProperty.customFormName : formName; // returned ex: Short Time Off
    },
    // function to display amount(negative -> inside (), empty -> -, original -> original)
    fnDisplayAmountValue(amount) {
      let finalAmount =
        amount || amount === 0 // check amount is exist or not
          ? Math.sign(amount) === -1 // check amount is negative
            ? "₹(" + Math.abs(amount) + ")" // true, add amount value inside ()
            : "₹" + amount // false, just display the original amount
          : "-"; // amount not exist then display hyphen
      return finalAmount;
    },
    //filter particular form url under employee modules
    employeeFormRedirectionUrl(formName) {
      let employeeForms = this.employeeFormsDetails[0]?.formList;
      if (employeeForms.length > 0) {
        let formDetail = employeeForms.filter((el) => el.formName === formName);
        if (formDetail.length > 0) {
          let actionPath = formDetail[0].url;
          var redirectionUrl = this.baseUrl + actionPath;
          window.location.href = redirectionUrl;
        }
      }
    },
    //filter particular form url under payroll modules
    payrollFormRedirectionUrl(formName) {
      let payrollForms = this.payrollFormsDetails[0]?.formList;
      if (payrollForms.length > 0) {
        let formDetail = payrollForms.filter((el) => el.formName === formName);
        if (formDetail.length > 0) {
          let actionPath = formDetail[0].url;
          var redirectionUrl = this.baseUrl + actionPath;
          window.location.href = redirectionUrl;
        }
      }
    },
    //check for view rights in forms for redirection
    //if they don't have view rights we will not do redirection
    isFormHasViewRights(formName) {
      let formattedFormName = formName
        ?.trim()
        .replace(/\s/g, "-")
        .toLowerCase();

      let form = this.formDetails(formattedFormName);
      if (form && form.accessRights) {
        let rights = form.accessRights;
        return rights.view === 1;
      }
      return false;
    },
    //Validate the employee pre approval request is approved for the work from home(work place) for the attendance date
    validateWfhPreApprovalRequest(attendanceDate, missedAttendance) {
      this.failedPreApprovals = [];
      this.validateWFHLoading = true;
      try {
        let vm = this;
        vm.$apollo
          .query({
            query: VALIDATE_WFH_PREAPPORVAL_REQUEST,
            variables: {
              employeeId: vm.loginEmployeeId,
              attendanceDate: attendanceDate,
              source: "attendanceregularization",
              punchInDateTime: "",
              punchOutDateTime: "",
              punchType: "",
              checkInCheckoutWorkPlace: "",
            },
            client: "apolloClientI",
            fetchPolicy: "no-cache",
          })
          .then(() => {
            vm.wfhRegularizationPreApprovalErrorMessage = "";
            vm.failedPreApprovals = [];
            vm.missedAttendanceData = missedAttendance;
            vm.isOpenAttendanceModal = true;
            this.validateWFHLoading = false;
          })
          .catch((error) => {
            this.validateWFHLoading = false;
            if (error && error.graphQLErrors) {
              let errorCode = getErrorCodesAndMessagesWithValidation(error);
              if (errorCode) {
                if (errorCode[0] === "CHR0057") {
                  let failedPreApprovals =
                    error?.graphQLErrors?.[0]?.extensions?.failedPreApprovals ||
                    [];
                  if (failedPreApprovals && failedPreApprovals.length > 0) {
                    failedPreApprovals = failedPreApprovals.map((preApproval) =>
                      preApproval.toLowerCase()
                    );
                    if (failedPreApprovals?.includes("work from home")) {
                      this.failedPreApprovals.push(1);
                    }
                    if (failedPreApprovals?.includes("on duty")) {
                      this.failedPreApprovals.push(9);
                    }
                  }
                  vm.missedAttendanceData = missedAttendance;
                  vm.isOpenAttendanceModal = true;
                } else {
                  let errorMessage = "";
                  if (
                    errorCode[0] === "CHR0056" ||
                    errorCode[0] === "CHR0066" ||
                    errorCode[0] === "CHR0067"
                  ) {
                    // "CHR0056": Work from home pre-approval request is not approved for the attendance date.
                    errorMessage = errorCode[1];
                  }
                  // "CHR0058": Error while validating the work from home pre-approval request.
                  // "CHR0059": Error while getting the employee timezone current date-time.
                  // "CHR0101": Error while processing the request to validate the work from home pre-approval request.
                  // "CHR0063": Error while getting the employee timezone current date-time.
                  vm.wfhPreApprovalRequestErrorHandling(errorMessage);
                }
              } else {
                vm.wfhPreApprovalRequestErrorHandling();
              }
            } else if (error && error.networkError) {
              this.wfhPreApprovalRequestErrorHandling(
                handleNetworkErrors(error)
              );
            } else {
              vm.wfhPreApprovalRequestErrorHandling();
            }
          });
      } catch {
        this.validateWFHLoading = false;
        this.wfhPreApprovalRequestErrorHandling();
      }
    },
    //Function to handle the error for work from home pre-approval request validation
    wfhPreApprovalRequestErrorHandling(message = "") {
      this.closeAttendanceModalForm();
      let snackbarData = {
        isOpen: true,
        message: "",
        type: "warning",
      };
      snackbarData.message = message
        ? message
        : "Something went wrong while validating the pre-approval request. Please try after some time.";
      this.showAlert(snackbarData);
    },
    closeAttendanceModalForm() {
      this.attendanceType = "";
      this.missedAttendanceData = "";
      this.isOpenAttendanceModal = false;
      this.failedPreApprovals = [];
    },
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style lang="scss" scoped>
/* Actions scrollbar height - responsive */
.actions-scrollbar {
  min-height: 240px;
  max-height: 280px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

/* Card layout improvements */
:deep(.v-card-text) {
  flex: 1;
  display: flex;
  flex-direction: column;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .actions-scrollbar {
    min-height: 280px;
    max-height: 500px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .actions-scrollbar {
    min-height: 230px;
    max-height: 330px;
  }
}
</style>
