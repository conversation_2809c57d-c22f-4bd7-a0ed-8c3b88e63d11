import gql from "graphql-tag";

// ===============
// Queries
// ===============
export const LIST_DOCUMENT_TEMPLATE = gql`
  query listDocumentTemplate {
    listDocumentTemplate {
      errorCode
      message
      documentTemplateDetails {
        documentTemplateId
        title
        registeredBusinessAddress
        templateContent
        formId
        formName
      }
    }
  }
`;

export const LIST_EMP_GENERATED_DOCUMENTS = gql`
  query listEmployeeGeneratedDocuments {
    listEmployeeGeneratedDocuments {
      errorCode
      message
      documentDetails {
        generatedDocumentId
        templateId
        templateName
        documentName
        employeeId
        employeeName
        userDefinedEmployeeId
        personalEmail
        employeeEmail
        mobileNumber
        candidateId
        candidateName
        userDefinedCandidateId
        candidatePersonalEmail
        candidateJobEmail
        documentContent
        documentLink
        registeredBusinessAddress
        authorizedSignatories
        status
        addedBy
        updatedBy
        addedOn
        updatedOn
      }
    }
  }
`;

export const LIST_DOCUMENT_TEMPLATE_FIELDS = gql`
  query listDocumentTemplateFields {
    listDocumentTemplateFields {
      errorCode
      message
      documentTemplateFieldDetails
    }
  }
`;

export const LIST_CUSTOM_COMPONENTS_IN_TEMPLATE = gql`
  query listTemplateCustomComponents($documentTemplateId: Int!) {
    listTemplateCustomComponents(documentTemplateId: $documentTemplateId) {
      errorCode
      message
      templateCustomComponents
    }
  }
`;

export const RETRIEVE_DOCUMENT_INPUTS = gql`
  query retrieveDocumentInputs(
    $documentTemplateId: Int!
    $registeredBusinessAddress: Int!
    $employeeId: Int!
    $candidateId: Int!
  ) {
    retrieveDocumentInputs(
      documentTemplateId: $documentTemplateId
      registeredBusinessAddress: $registeredBusinessAddress
      employeeId: $employeeId
      candidateId: $candidateId
    ) {
      errorCode
      message
      documentDetails
      businessAddress {
        street1
        street2
        cityId
        stateId
        countryCode
        pincode
        state
        city
        country
      }
    }
  }
`;

export const LIST_EMPLOYEES_IN_GENERATOR = gql`
  query listEmployeesInGenerator(
    $source: String!
    $fetchManagerAndAdmin: Int!
  ) {
    listEmployeesInGenerator(
      fetchManagerAndAdmin: $fetchManagerAndAdmin
      source: $source
    ) {
      errorCode
      message
      employeeDetails {
        employeeId
        userDefinedEmployeeId
        employeeName
      }
    }
  }
`;

export const LIST_CANDIDATES = gql`
  query listCandidates {
    listCandidates {
      errorCode
      message
      candidateDetails {
        candidateId
        candidateName
      }
    }
  }
`;

export const LIST_TEMPLATES_IN_DROPDOWN = gql`
  query listTemplatesInDropdown {
    listTemplatesInDropdown {
      errorCode
      message
      documentTemplateDetails {
        documentTemplateId
        title
        templateContent
        registeredBusinessAddress
        entity
      }
    }
  }
`;

export const RETRIEVE_SIGNATURE_DOCUMENT_DETAILS = gql`
  query retrieveSignatoryAndDocumentDetails($authorizerDetails: String!) {
    retrieveSignatoryAndDocumentDetails(authorizerDetails: $authorizerDetails) {
      errorCode
      message
      authorizerAndDocumentDetails {
        generatedDocumentId
        documentName
        documentContent
        signatureFileName
        authorizerDetails {
          signatureLevelId
          signatureKey
          signatureEmployeeId
          signatureEmployeeName
          status
        }
      }
    }
  }
`;

export const SEND_DOCUMENT_IN_EMAIL = gql`
  query sendDocumentInEmail(
    $documentId: Int!
    $s3DocumentName: String!
    $emailSubject: String!
    $emailBody: String!
    $toEmailIds: [String]!
    $ccEmailIds: [String]
  ) {
    sendDocumentInEmail(
      documentId: $documentId
      s3DocumentName: $s3DocumentName
      emailSubject: $emailSubject
      emailBody: $emailBody
      toEmailIds: $toEmailIds
      ccEmailIds: $ccEmailIds
    ) {
      errorCode
      message
    }
  }
`;

export const RESEND_EMAIL_TO_SIGNATORIES = gql`
  query resendEmailToSignatory(
    $documentId: Int!
    $documentName: String!
    $signatoryId: Int!
    $signatoryEmailAddress: String!
    $signatoryName: String!
    $isCandidate: Int
  ) {
    resendEmailToSignatory(
      documentId: $documentId
      documentName: $documentName
      signatoryId: $signatoryId
      signatoryEmailAddress: $signatoryEmailAddress
      signatoryName: $signatoryName
      isCandidate: $isCandidate
    ) {
      errorCode
      message
    }
  }
`;

export const GET_REPORT_DEFINITIONS_LIST = gql`
  query getReportDefinitionsList($limit: Int, $offset: Int, $formId: Int) {
    getReportDefinitionsList(limit: $limit, offset: $offset, formId: $formId) {
      errorCode
      message
      totalRecords
      reportDefinitions {
        Report_Id
        Report_Title
        Report_Group_Name
        Custom_Report_Title
      }
    }
  }
`;

export const GET_REPORT_DEFINITION_DETAILS = gql`
  query getReportDefinitionDetails($reportId: Int!, $formId: Int!) {
    getReportDefinitionDetails(reportId: $reportId, formId: $formId) {
      errorCode
      message
      reportDetails {
        Report_Id
        Report_Title
        Report_Group_Name
        Custom_Report_Title
        Report_Type
        Docu_Sign_Placeholder
      }
    }
  }
`;

// ===============
// Mutation
// ===============
export const ADD_UPDATE_DOCUMENT_TEMPLATE = gql`
  mutation addUpdateDocumentTemplate(
    $documentTemplateId: Int!
    $title: String!
    $templateContent: String!
    $signatoryKeys: [String]!
    $registeredBusinessAddress: Int!
    $formId: Int
  ) {
    addUpdateDocumentTemplate(
      documentTemplateId: $documentTemplateId
      title: $title
      templateContent: $templateContent
      signatoryKeys: $signatoryKeys
      registeredBusinessAddress: $registeredBusinessAddress
      formId: $formId
    ) {
      errorCode
      message
    }
  }
`;
export const UPDATE_OFFER_LETTER_STATUS = gql`
  mutation updateOfferLetterStatus(
    $status: String!
    $documentId: Int!
    $candidateId: Int
    $signatureKey: String
  ) {
    updateOfferLetterStatus(
      status: $status
      documentId: $documentId
      candidateId: $candidateId
      signatureKey: $signatureKey
    ) {
      errorCode
      message
    }
  }
`;

export const DELETE_DOCUMENT_TEMPLATE = gql`
  mutation deleteDocumentTemplate($documentTemplateId: Int!) {
    deleteDocumentTemplate(documentTemplateId: $documentTemplateId) {
      errorCode
      message
    }
  }
`;

export const DELETE_EMPLOYEE_GENERATED_DOCUMENT = gql`
  mutation deleteEmployeeGeneratedDocuments($generatedDocumentId: Int!) {
    deleteEmployeeGeneratedDocuments(
      generatedDocumentId: $generatedDocumentId
    ) {
      errorCode
      message
    }
  }
`;

export const GENERATE_UPDATE_DOCUMENT_FOR_EMPLOYEE = gql`
  mutation addUpdateEmployeeGeneratedDocument(
    $generatedDocumentId: Int!
    $employeeId: Int!
    $candidateId: Int!
    $templateName: String
    $documentName: String!
    $registeredBusinessAddress: String!
    $documentContent: String!
    $customComponents: [customComponentsInput]!
    $signingAuthorities: String
    $status: String!
    $additionalDetails: String
  ) {
    addUpdateEmployeeGeneratedDocument(
      generatedDocumentId: $generatedDocumentId
      employeeId: $employeeId
      candidateId: $candidateId
      templateName: $templateName
      documentName: $documentName
      registeredBusinessAddress: $registeredBusinessAddress
      documentContent: $documentContent
      customComponents: $customComponents
      signingAuthorities: $signingAuthorities
      status: $status
      additionalDetails: $additionalDetails
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_SIGNATURE_DETAILS = gql`
  mutation updateDocumentDetails(
    $authorizerDetails: authorizerDetailsInput!
    $documentId: Int!
  ) {
    updateDocumentDetails(
      authorizerDetails: $authorizerDetails
      documentId: $documentId
    ) {
      errorCode
      message
      documentDetails
    }
  }
`;

export const UPLOAD_SIGNATURE_DOCUMENT = gql`
  mutation uploadEmployeeGeneratedDocument(
    $authorizerDetails: [authorizerDetailsInput]!
    $documentId: Int!
    $documentName: String!
    $signatureFileName: String
    $signatoryEmployeeId: Int!
    $signatoryKey: String!
    $pdfDocumentName: String!
    $signedCount: Int!
  ) {
    uploadEmployeeGeneratedDocument(
      authorizerDetails: $authorizerDetails
      documentId: $documentId
      documentName: $documentName
      signatureFileName: $signatureFileName
      signatoryEmployeeId: $signatoryEmployeeId
      signatoryKey: $signatoryKey
      pdfDocumentName: $pdfDocumentName
      signedCount: $signedCount
    ) {
      errorCode
      message
    }
  }
`;

export const UPDATE_FILE_NAME = gql`
  mutation updateFileName(
    $documentId: Int!
    $fileName: String!
    $saveSignature: Int!
    $employeeId: Int!
    $candidateId: Int!
  ) {
    updateFileName(
      documentId: $documentId
      fileName: $fileName
      saveSignature: $saveSignature
      employeeId: $employeeId
      candidateId: $candidateId
    ) {
      errorCode
      message
    }
  }
`;
