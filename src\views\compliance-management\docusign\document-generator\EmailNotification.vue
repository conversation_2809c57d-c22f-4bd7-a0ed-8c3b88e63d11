<template>
  <v-dialog
    v-model="openSendEmailModal"
    width="700"
    persistent
    @click:outside="closeModal()"
  >
    <v-card :min-width="isMobileView ? '' : 400" class="pb-4">
      <v-card-title
        class="d-flex align-center justify-space-between text-primary"
      >
        <span class="text-h6">Send Document</span>
        <v-btn
          icon="mdi-close"
          variant="text"
          color="primary"
          @click="closeModal()"
        />
      </v-card-title>

      <v-divider />

      <v-alert
        v-if="showWarningAlert"
        variant="text"
        density="compact"
        type="warning"
        class="mb-0"
        closable
        @click:close="closeAlert()"
      >
        {{ alertMessage }}
      </v-alert>
      <VuePerfectScrollbar class="scroll-area" :settings="scrollbarSettings">
        <div style="max-height: 700px" class="pa-5">
          <v-form
            ref="emailTemplateForm"
            v-model="formValid"
            class="d-flex flex-column ga-4"
          >
            <!-- To Email Field -->
            <div class="d-flex flex-column flex-sm-row align-sm-center ga-3">
              <v-label
                class="text-subtitle-2 flex-shrink-0"
                style="min-width: 60px"
              >
                To
              </v-label>
              <div class="flex-grow-1">
                <v-combobox
                  v-model="toEmailAddress"
                  :items="[]"
                  chips
                  clearable
                  multiple
                  :rules="toEmailRules"
                  color="primary"
                  placeholder="Enter email addresses"
                >
                  <template #chip="{ props, item }">
                    <v-chip
                      v-bind="props"
                      closable
                      color="primary"
                      @click:close="removeToEmailAddress(item.raw)"
                    >
                      <span
                        :style="
                          isMobileView ? 'max-width: 150px' : 'max-width: 300px'
                        "
                        class="text-truncate"
                      >
                        <strong>{{ item.raw }}</strong>
                      </span>
                    </v-chip>
                  </template>
                </v-combobox>
              </div>
              <v-btn
                v-if="!showCC"
                variant="text"
                size="small"
                color="primary"
                prepend-icon="mdi-plus"
                class="flex-shrink-0"
                @click="showCC = true"
              >
                Add CC
              </v-btn>
            </div>
            <!-- CC Email Field -->
            <div
              v-if="showCC"
              class="d-flex flex-column flex-sm-row align-sm-center ga-3"
            >
              <v-label
                class="text-subtitle-2 flex-shrink-0"
                style="min-width: 60px"
              >
                CC
              </v-label>
              <div class="flex-grow-1">
                <v-combobox
                  v-model="ccEmailAddress"
                  :items="[]"
                  chips
                  clearable
                  multiple
                  :rules="ccEmailRules"
                  color="primary"
                  placeholder="Enter CC email addresses"
                >
                  <template #chip="{ props, item }">
                    <v-chip
                      v-bind="props"
                      closable
                      color="primary"
                      @click:close="removeCCEmailAddress(item.raw)"
                    >
                      <span
                        :style="
                          isMobileView ? 'max-width: 150px' : 'max-width: 300px'
                        "
                        class="text-truncate"
                      >
                        <strong>{{ item.raw }}</strong>
                      </span>
                    </v-chip>
                  </template>
                </v-combobox>
              </div>
            </div>
            <!-- Subject Field -->
            <div class="d-flex flex-column flex-sm-row align-sm-center ga-3">
              <v-label
                class="text-subtitle-2 flex-shrink-0"
                style="min-width: 60px"
              >
                Subject
              </v-label>
              <div class="flex-grow-1">
                <v-text-field
                  id="email-subject"
                  v-model="emailSubject"
                  counter
                  maxlength="150"
                  :rules="subjectRules"
                  color="primary"
                  density="comfortable"
                />
              </div>
            </div>
            <!-- Body Field -->
            <div class="d-flex flex-column flex-sm-row align-sm-start ga-3">
              <v-label
                class="text-subtitle-2 flex-shrink-0 mt-3"
                style="min-width: 60px"
              >
                Body
              </v-label>
              <div class="flex-grow-1">
                <v-textarea
                  id="email-template-content"
                  v-model="emailBody"
                  counter
                  maxlength="500"
                  :rules="bodyRules"
                  rows="4"
                  color="primary"
                  density="comfortable"
                />
              </div>
            </div>
          </v-form>
        </div>
      </VuePerfectScrollbar>

      <v-card-actions class="justify-center pa-4">
        <v-btn color="primary" variant="outlined" @click="closeModal()">
          Cancel
        </v-btn>
        <v-btn
          color="primary"
          variant="flat"
          class="ml-2"
          @click="sendDocumentInMail()"
        >
          Send
        </v-btn>
      </v-card-actions>
    </v-card>
    <AppLoading v-if="loadingScreen" />
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted, getCurrentInstance } from "vue";
import { useStore } from "vuex";
import { getErrorCodesWithValidation, handleNetworkErrors } from "@/helper";
import { SEND_DOCUMENT_IN_EMAIL } from "@/graphql/compliance-management/docuSignQueries";

// Props
const props = defineProps({
  notifyDocumentDetails: {
    type: Object,
    required: true,
  },
  openEmailModal: {
    type: Boolean,
    required: true,
  },
});

// Emits
const emit = defineEmits(["close-notify-modal"]);

// Composables
const store = useStore();
const instance = getCurrentInstance();

// Reactive data
const openSendEmailModal = ref(false);
const emailSubject = ref("");
const emailBody = ref("");
const loadingScreen = ref(false);
const toEmailAddress = ref([]);
const ccEmailAddress = ref([]);
const alertMessage = ref("");
const showWarningAlert = ref(false);
const showCC = ref(false);
const formValid = ref(false);
const emailTemplateForm = ref(null);

// Computed properties
const scrollbarSettings = computed(() => {
  return store.state.scrollbarSettings;
});

const isMobileView = computed(() => {
  return store.state.isMobileWindowSize;
});

// Validation rules
const toEmailRules = computed(() => [
  (v) => (v && v.length > 0) || "At least one email address is required",
  (v) => {
    if (!v || v.length === 0) return true;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (
      v.every((email) => emailRegex.test(email)) ||
      "Please enter valid email addresses"
    );
  },
]);

const ccEmailRules = computed(() => [
  (v) => {
    if (!v || v.length === 0) return true;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return (
      v.every((email) => emailRegex.test(email)) ||
      "Please enter valid email addresses"
    );
  },
]);

const subjectRules = computed(() => [
  (v) => !!v || "Subject is required",
  (v) => (v && v.length >= 5) || "Subject must be at least 5 characters",
  (v) => (v && v.length <= 150) || "Subject must be less than 150 characters",
]);

const bodyRules = computed(() => [
  (v) => !!v || "Email body is required",
  (v) => (v && v.length >= 5) || "Email body must be at least 5 characters",
  (v) =>
    (v && v.length <= 500) || "Email body must be less than 500 characters",
]);

// Watchers
watch(showWarningAlert, (val) => {
  if (val) {
    setTimeout(() => {
      closeAlert();
    }, 3000);
  }
});

watch(
  () => props.openEmailModal,
  (newVal) => {
    openSendEmailModal.value = newVal;
  }
);

watch(
  () => props.notifyDocumentDetails,
  (newVal) => {
    if (newVal) {
      initializeEmailData();
    }
  },
  { immediate: true }
);

// Lifecycle
onMounted(() => {
  openSendEmailModal.value = props.openEmailModal;
  initializeEmailData();
});

// Methods
const initializeEmailData = () => {
  // Reset email data
  toEmailAddress.value = [];
  ccEmailAddress.value = [];
  emailSubject.value = "";
  emailBody.value = "";

  // Safely get user details
  const { employeeFullName, employeeEmail } = store.state.userDetails || {};

  // Safely get document details
  const documentDetails = props.notifyDocumentDetails || {};
  const {
    candidatePersonalEmail,
    candidateJobEmail,
    personalEmail,
    documentName,
    employeeEmail: docEmployeeEmail,
  } = documentDetails;

  // Add current user's email as default recipient
  if (employeeEmail) {
    toEmailAddress.value.push(employeeEmail);
  }

  // Add document-specific emails if they exist
  if (docEmployeeEmail && docEmployeeEmail !== employeeEmail) {
    toEmailAddress.value.push(docEmployeeEmail);
  }
  if (personalEmail) {
    toEmailAddress.value.push(personalEmail);
  }
  if (candidateJobEmail) {
    toEmailAddress.value.push(candidateJobEmail);
  }
  if (candidatePersonalEmail) {
    toEmailAddress.value.push(candidatePersonalEmail);
  }

  // Set default subject and body
  const senderName = employeeFullName || "Someone";
  const docName = documentName || "a document";
  emailSubject.value = `${senderName} sent you ${docName}`;
  emailBody.value = `Please find attached ${docName}`;
};

const closeAlert = () => {
  showWarningAlert.value = false;
  alertMessage.value = "";
};

const closeModal = () => {
  openSendEmailModal.value = false;
  emit("close-notify-modal");
};

const removeToEmailAddress = (item) => {
  toEmailAddress.value.splice(toEmailAddress.value.indexOf(item), 1);
  toEmailAddress.value = [...toEmailAddress.value];
};

const removeCCEmailAddress = (item) => {
  ccEmailAddress.value.splice(ccEmailAddress.value.indexOf(item), 1);
  ccEmailAddress.value = [...ccEmailAddress.value];
};

const sendDocumentInMail = async () => {
  const { valid } = await emailTemplateForm.value.validate();
  if (valid) {
    loadingScreen.value = true;
    try {
      // Safely extract document details
      const documentDetails = props.notifyDocumentDetails || {};
      const { generatedDocumentId, documentLink } = documentDetails;

      // Validate required fields
      if (!generatedDocumentId || !documentLink) {
        showAlert({
          isOpen: true,
          type: "error",
          message: "Missing document information. Please try again.",
        });
        loadingScreen.value = false;
        return;
      }

      instance.proxy.$apollo
        .query({
          query: SEND_DOCUMENT_IN_EMAIL,
          variables: {
            documentId: generatedDocumentId,
            s3DocumentName: documentLink,
            emailSubject: emailSubject.value,
            emailBody: emailBody.value,
            toEmailIds: toEmailAddress.value,
            ccEmailIds: ccEmailAddress.value,
          },
          client: "apolloClientO",
          fetchPolicy: "network-only",
        })
        .then((response) => {
          loadingScreen.value = false;
          const { errorCode, message } =
            response.data.sendDocumentInEmail || {};

          if (!errorCode) {
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Document has been mailed successfully",
            };
            showAlert(snackbarData);
            closeModal();
          } else {
            let snackbarData = {
              isOpen: true,
              type: "warning",
              message: message || "Failed to send document email",
            };
            showAlert(snackbarData);
          }
        })
        .catch((mailSendErr) => {
          handleDocumentSendError(mailSendErr);
        });
    } catch {
      handleDocumentSendError();
    }
  }
};

// handle document email send error
const handleDocumentSendError = (err = "") => {
  loadingScreen.value = false;
  // check if it is a graphql error
  if (err && err.graphQLErrors) {
    let errorCode = getErrorCodesWithValidation(err);
    if (errorCode) {
      switch (errorCode[0]) {
        case "_DB0000": // technical issues.
          alertMessage.value =
            "It’s us! There seems to be some technical difficulties. Please try after some time.";
          break;
        case "CDG0116": // Empty file retrieved from s3.
          alertMessage.value =
            "Unable to send the document as it was deleted already or the document does not exist.";
          break;
        case "PBP0105": // Error in sending email
          alertMessage.value =
            "There seems to be some technical difficulties while sending an email. Please try after some time.";
          break;
        // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
        case "BAD_USER_INPUT":
        case "IVE0000":
          var validationErrors = errorCode[1];
          // add all the backend validation error messages as single sentence to present it to the users
          var validationMessages = "";
          if (validationErrors) {
            for (var errCode in validationErrors) {
              //  Please provide a valid share value.
              if (
                errCode === "IVE0226" || // Please provide a valid to email address.
                errCode === "IVE0227" || // Only 50 email addresses are allowed.
                errCode === "IVE0117" || // Please enter at least 5 characters and not more than 500 characters.
                errCode === "IVE0116" // Please enter at least 5 characters and not more than 150 characters.
              ) {
                validationMessages = validationErrors[errCode];
              }
            }
          }
          if (validationMessages) {
            alertMessage.value = validationMessages;
          } else {
            // IVE0225 - Please provide a valid from email address.
            alertMessage.value =
              "Something went wrong while updating currency and share value. If you continue to see this issue, please contact the platform administrator.";
          }
          break;
        case "PBP0110": // Error in retrieve file from s3
        case "CDG0014": // Error while sending the document in email
        case "_EC0007": // Invalid input field(s).
        default:
          alertMessage.value =
            "Something went wrong while sending document to employees. If you continue to see this issue, please contact the platform administrator.";
          break;
      }
    } else {
      alertMessage.value =
        "Something went wrong while sending document to employees. Please try after some time.";
    }
  } else if (err && err.networkError) {
    alertMessage.value = handleNetworkErrors(err);
  } else {
    alertMessage.value =
      "Something went wrong while sending document to employees. Please try after some time.";
  }
  showWarningAlert.value = true;
};

// show the message in snack bar
const showAlert = (snackbarData) => {
  store.commit("OPEN_SNACKBAR", snackbarData);
};
</script>
