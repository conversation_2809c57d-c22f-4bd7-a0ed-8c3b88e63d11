<template>
  <div>
    <div
      class="d-flex align-center mb-4"
      :class="isMobileView ? 'justify-center' : 'justify-end'"
    >
      <v-btn
        v-if="canAddDocument"
        class="mr-3 ml-2"
        color="primary"
        @click="$emit('open-add-form')"
      >
        <v-icon class="mr-2"> fas fa-plus </v-icon>
        Add New
      </v-btn>
      <v-btn
        rounded="lg"
        color="transparent"
        variant="flat"
        class="ml-0"
        @click="$emit('refetch-list')"
      >
        <v-icon>fas fa-redo-alt</v-icon>
      </v-btn>
    </div>
    <v-data-table
      v-if="tableItems.length > 0"
      id="doc_template_table"
      :headers="tableHeaders"
      :items="tableItems"
      :items-per-page="50"
      :items-per-page-options="[
        { value: 50, title: '50' },
        { value: 100, title: '100' },
        {
          value: -1,
          title: '$vuetify.dataFooter.itemsPerPageAll',
        },
      ]"
      fixed-header
      :sort-by="[{ key: 'employeeName', order: 'asc' }]"
      :height="tableItems.length > 11 ? $store.getters.getTableHeight(270) : ''"
      class="elevation-1"
      style="box-shadow: none !important"
    >
      <template #no-results>
        <AppFetchErrorScreen
          key="no-results-screen"
          button-text="Clear All"
          icon-name="fas fa-sync"
          main-title="No matching search results found."
          content="Please try again by changing the filters or clear it to get all data."
          image-name="common/no-records"
          @button-click="clearFilter()"
        />
      </template>
      <template v-slot:item="{ item }">
        <tr
          :id="'doc_template_list__tr_' + item.generatedDocumentId"
          style="z-index: 200; cursor: pointer"
          class="data-table-tr bg-white cursor-pointer"
          @click="selectDocTemplateRecord(item)"
          :class="[
            isMobileView ? ' v-data-table__mobile-table-row ma-0 mt-2' : 'pr-6',
          ]"
        >
          <!-- Employee Name -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Employee Name
            </div>
            <section style="height: 3em" class="d-flex align-center">
              <div class="d-flex align-center">
                <div
                  v-if="windowWidth > 1264"
                  class="data-table-side-border d-flex"
                  style="height: 3em"
                ></div>
              </div>
              <span
                class="text-primary text-body-2 font-weight-regular text-truncate"
              >
                <section :class="isMobileView ? '' : 'text-truncate'">
                  <span class="text-primary font-weight-regular">
                    {{ checkNullValue(item.employeeName) }}
                  </span>
                  <div
                    v-if="!isMobileView && item.userDefinedEmployeeId"
                    class="text-caption text-grey-darken-1 mt-1"
                  >
                    {{ item.userDefinedEmployeeId }}
                  </div>
                </section>
              </span>
            </section>
          </td>

          <!-- Candidate Name -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Candidate Name
            </div>
            <section>
              <span class="text-body-2 font-weight-medium text-grey-darken-3">
                {{ checkNullValue(item.candidateName) }}
              </span>
              <div
                v-if="!isMobileView && item.userDefinedCandidateId"
                class="text-caption text-grey-darken-1 mt-1"
              >
                {{ item.userDefinedCandidateId }}
              </div>
            </section>
          </td>

          <!-- Document Name -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Document Name
            </div>
            <section>
              <span class="text-body-2 text-primary font-weight-regular">
                {{ checkNullValue(item.documentName) }}
              </span>
            </section>
          </td>

          <!-- Signatories -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Signatories
            </div>
            <section>
              <div class="d-flex align-center">
                <div
                  v-if="
                    item.authorizedSignatories &&
                    item.authorizedSignatories.length > 0
                  "
                  class="d-flex align-center"
                >
                  <v-menu :open-on-hover="!isMobileView" location="bottom">
                    <template #activator="{ props }">
                      <div v-bind="props" class="d-flex align-center">
                        <v-avatar
                          v-for="(signatory, index) in JSON.parse(
                            item.authorizedSignatories || '[]'
                          )"
                          :key="index + 'avatar'"
                          size="32"
                          :color="
                            signatory.status === 'Signed' ? 'success' : 'grey'
                          "
                          class="mr-1 text-caption text-white font-weight-bold"
                        >
                          {{ letterAvatar(signatory.signatureEmployeeName) }}
                        </v-avatar>
                      </div>
                    </template>

                    <v-list>
                      <v-list-item
                        v-for="(signatory, index) in JSON.parse(
                          item.authorizedSignatories || '[]'
                        )"
                        :key="index + 'menuList'"
                      >
                        <template #prepend>
                          <v-avatar
                            size="30"
                            :color="
                              signatory.status === 'Signed' ? 'success' : 'grey'
                            "
                            class="text-caption text-white font-weight-bold"
                          >
                            {{ letterAvatar(signatory.signatureEmployeeName) }}
                          </v-avatar>
                        </template>

                        <v-list-item-title class="text-truncate">
                          {{ signatory.signatureEmployeeName }}
                        </v-list-item-title>
                        <v-list-item-subtitle
                          :class="
                            signatory.status === 'Signed'
                              ? 'text-success'
                              : signatory.status === 'Not Signed'
                              ? 'text-grey-darken-2'
                              : 'text-grey-darken-1'
                          "
                        >
                          {{ signatory.status }}
                        </v-list-item-subtitle>
                        <template #append>
                          <v-btn
                            v-if="
                              signatory.status === 'Not Signed' &&
                              signatory.emailId &&
                              !['Declined', 'Rejected', 'Draft'].includes(
                                item.status
                              )
                            "
                            color="primary"
                            size="small"
                            class="mr-1"
                            :disabled="
                              signatory?.signatureKey?.toLowerCase() ===
                                'candidate' &&
                              !checkIfSignedByAll(
                                JSON.parse(item?.authorizedSignatories || '[]')
                              )
                            "
                            @click.stop="
                              resendEmailToSignatories(item, signatory)
                            "
                          >
                            {{ signatory?.emailSent ? "Resend" : "Send" }}
                          </v-btn>
                        </template>
                      </v-list-item>
                    </v-list>
                  </v-menu>
                </div>
                <div v-else class="text-grey-darken-1">-</div>
              </div>
            </section>
          </td>

          <!-- Status -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Status
            </div>
            <section>
              <div class="d-flex align-center">
                <!-- Status Icon -->
                <v-icon
                  v-if="item.status === 'Completed'"
                  color="success"
                  size="18"
                  class="mr-2"
                  icon="fas fa-check-circle"
                ></v-icon>
                <v-icon
                  v-else-if="item.status === 'Partially Signed'"
                  color="primary"
                  size="18"
                  class="mr-2"
                  icon="fas fa-check-circle"
                ></v-icon>
                <v-icon
                  v-else-if="item.status === 'In Review'"
                  color="warning"
                  size="18"
                  class="mr-2"
                  icon="fas fa-search"
                ></v-icon>
                <v-icon
                  v-else-if="item.status === 'Draft'"
                  color="purple"
                  size="18"
                  class="mr-2"
                  icon="fas fa-pencil-alt"
                ></v-icon>
                <v-icon
                  v-else-if="
                    item.status === 'Declined' || item.status === 'Rejected'
                  "
                  color="error"
                  size="18"
                  class="mr-2"
                  icon="fas fa-times-circle"
                ></v-icon>
                <v-icon
                  v-else
                  color="grey-darken-1"
                  size="18"
                  class="mr-2"
                  icon="fas fa-question-circle"
                ></v-icon>

                <!-- Status Text -->
                <span
                  class="text-body-2 font-weight-medium"
                  :class="
                    item.status === 'Completed'
                      ? 'text-success'
                      : item.status === 'In Review'
                      ? 'text-warning'
                      : item.status === 'Partially Signed'
                      ? 'text-primary'
                      : item.status === 'Draft'
                      ? 'text-purple'
                      : item.status === 'Declined' || item.status === 'Rejected'
                      ? 'text-error'
                      : 'text-grey-darken-1'
                  "
                >
                  {{ item.status }}
                </span>
              </div>
            </section>
          </td>

          <!-- Document Author -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2 pl-5 font-weight-small'
            "
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Document Author
            </div>
            <section>
              <span class="text-body-2 font-weight-regular text-grey-darken-3">
                {{ checkNullValue(item.addedBy) }}
              </span>
            </section>
          </td>

          <!-- Action -->
          <td
            :class="
              isMobileView
                ? 'd-flex justify-space-between align-center'
                : 'pa-2'
            "
            @click.stop=""
          >
            <div
              v-if="isMobileView"
              :class="
                isMobileView
                  ? ' font-weight-bold d-flex align-center'
                  : ' font-weight-bold mt-2 d-flex align-center'
              "
            >
              Actions
            </div>
            <section>
              <div class="d-flex justify-center">
                <ActionMenu
                  v-if="getActions(item)?.length"
                  @selected-action="onActions($event, item)"
                  :actions="getActions(item)"
                  :access-rights="checkAccess"
                  iconColor="grey"
                ></ActionMenu>
                <section class="text-body-2 font-weight-medium" v-else>
                  -
                </section>
              </div>
            </section>
          </td>
        </tr>
      </template>
    </v-data-table>
    <AppWarningModel
      v-if="openDeleteConfirmation"
      :open-delete-confirmation="openDeleteConfirmation"
      image-name="common/delete-bin"
      close-button-text="Cancel"
      accept-button-text="Proceed"
      confirmation-content="Are you sure to delete the selected record?"
      @close-warning-modal="closeDeleteConfirmationModal()"
      @accept-modal="deleteEmpDocument()"
    />
    <AppLoading v-if="loadingScreen" />
    <DocGeneratorEmailNotification
      v-if="openSendEmailModal && notifyDocumentDetails"
      :open-email-modal="openSendEmailModal"
      :notify-document-details="notifyDocumentDetails"
      @close-notify-modal="openSendEmailModal = false"
    />
  </div>
</template>

<script>
import { checkNullValue, getErrorCodes, handleNetworkErrors } from "@/helper";
import {
  DELETE_EMPLOYEE_GENERATED_DOCUMENT,
  RESEND_EMAIL_TO_SIGNATORIES,
} from "@/graphql/compliance-management/docuSignQueries";
import { GET_PRESIGNED_URL } from "@/graphql/commonQueries";
import ActionMenu from "@/components/custom-components/ActionMenu.vue";
import DocGeneratorEmailNotification from "./EmailNotification.vue";

export default {
  name: "ListDocumentGenerator",

  components: {
    ActionMenu,
    DocGeneratorEmailNotification,
  },

  emits: [
    "open-add-form",
    "refetch-list",
    "action-on-doc-generator",
    "delete-success",
  ],

  props: {
    tableItems: {
      type: Array,
      required: true,
    },
    formAccess: {
      type: [Object, Boolean],
      required: true,
    },
  },

  data: () => ({
    // pagination
    pageCount: 1,
    page: 1,
    itemsPerPage: 50,
    pageNumber: 50,
    itemsPerPageOptions: [50, 100, -1],
    openPrintModal: false,
    sortBy: [{ key: "employeeName", order: "asc" }],
    tableHeaders: [
      {
        title: "Employee Name",
        key: "employeeName",
        sortable: true,
      },
      {
        title: "Candidate Name",
        key: "candidateName",
        sortable: true,
      },
      {
        title: "Document Name",
        key: "documentName",
        sortable: true,
      },
      {
        title: "Signatories",
        key: "authorizedSignatories",
        sortable: false,
      },
      {
        title: "Status",
        key: "status",
        sortable: true,
      },
      {
        title: "Document Author",
        key: "documentAuthor",
        sortable: true,
      },
      {
        title: "Action",
        key: "action",
        align: "center",
        sortable: false,
      },
    ],
    selectedRecords: [],
    selectedRecord: {},
    openDeleteConfirmation: false,
    errorContent: "",
    deleteEmpDocId: null,
    loadingScreen: false,
    openSendEmailModal: false,
    notifyDocumentDetails: null,
  }),

  computed: {
    // search in shares list
    searchValue() {
      return this.$store.state.empSearchValue;
    },

    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    windowWidth() {
      return this.$store.state.windowWidth;
    },

    // Check if user can add new documents
    canAddDocument() {
      // If formAccess is false (Boolean), deny access
      if (this.formAccess === false) {
        return false;
      }

      // If formAccess is an object, check the add property
      if (typeof this.formAccess === "object" && this.formAccess !== null) {
        // If add is explicitly false, deny access
        // If add is undefined, null, or true, allow access
        return this.formAccess.add !== false;
      }

      return Boolean(this.formAccess);
    },

    // Check access rights for actions
    checkAccess() {
      const havingAccess = {};
      havingAccess["print"] = 1; // Always allow print
      havingAccess["pdf"] = 1; // Always allow PDF download if available
      havingAccess["email"] = 1; // Always allow email if available
      havingAccess["delete"] =
        this.formAccess && this.formAccess.delete ? this.formAccess.delete : 0;

      return havingAccess;
    },

    // Get available actions for each item based on status and permissions
    getActions() {
      return (item) => {
        const optionMenu = [];

        // Always show Print action
        optionMenu.push("Print");

        // Show PDF action only if document is completed and has a link
        if (item.documentLink && item.status === "Completed") {
          optionMenu.push("PDF");
        }

        // Show Email action only if document is completed
        if (item.status === "Completed") {
          optionMenu.push("Email");
        }

        // Show Delete action only if document is not completed and user has delete access
        if (item.status !== "Completed" && this.formAccess?.delete) {
          optionMenu.push("Delete");
        }

        return optionMenu;
      };
    },
  },

  watch: {
    // when page number is changed
    pageNumber(val) {
      if (val === "All") {
        this.pageCount = 1;
        this.itemsPerPage = this.tableItems.length;
        this.page = 1;
      } else {
        let pageCount = this.tableItems.length / this.pageNumber;
        pageCount = pageCount <= 1 ? 1 : pageCount;
        this.pageCount = Math.round(pageCount);
        this.itemsPerPage = this.pageNumber;
        this.page = 1;
      }
    },
  },

  methods: {
    checkNullValue,
    // pageNumber is emitted from its child component to update the count in parent
    fnChangePaginationCount(val) {
      this.pageNumber = val;
    },

    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },

    // select the record to view
    selectDocTemplateRecord(selectedRecord) {
      this.selectedRecord = selectedRecord;
      // Emit event to parent component to switch to view mode
      this.$emit("action-on-doc-generator", [selectedRecord, "view"]);
    },

    downloadPDF(selectedRecord) {
      this.$emit("action-on-doc-generator", [selectedRecord, "download"]);
    },

    // clear search and filter data
    clearFilter() {
      this.$store.commit("UPDATE_TOPBAR_CLEAR_FLAG", true);
      this.$store.commit("UPDATE_EMP_SEARCH_VAL", "");
    },

    // Handle action selection from ActionMenu
    onActions(action, item) {
      const actionLower = action.toLowerCase();

      if (actionLower === "print") {
        this.onClickPrint(item);
      } else if (actionLower === "pdf") {
        this.downloadPDF(item);
      } else if (actionLower === "email") {
        this.sendEmailToEmployee(item);
      } else if (actionLower === "delete") {
        this.openDeleteConfirmationModal(item.generatedDocumentId);
      }
    },

    // on delete employees generated document
    deleteEmpDocument() {
      let vm = this;
      vm.loadingScreen = true;
      vm.closeDeleteConfirmationModal();
      try {
        vm.$apollo
          .mutate({
            mutation: DELETE_EMPLOYEE_GENERATED_DOCUMENT,
            variables: {
              generatedDocumentId: vm.deleteEmpDocId,
            },
            client: "apolloClientP",
          })
          .then((result) => {
            vm.loadingScreen = false;
            let snackbarData = {
              isOpen: true,
              type: "success",
              message: "Document deleted successfully.",
            };
            vm.clearFilter();
            vm.showAlert(snackbarData);
            vm.deleteEmpDocId = null;
            vm.$emit("delete-success");
          })
          .catch((deleteError) => {
            vm.handleDeleteError(deleteError);
          });
      } catch {
        vm.handleDeleteError();
      }
    },

    // resend email to signatories
    resendEmailToSignatories(selectedItem, selectedSignatory) {
      let vm = this;
      vm.loadingScreen = true;
      try {
        const { generatedDocumentId, documentName } = selectedItem;
        const {
          signatureEmployeeId,
          signatureEmployeeName,
          emailId,
          signatureKey,
        } = selectedSignatory;
        let isCandidate = signatureKey === "Candidate" ? 1 : 0;
        vm.$apollo
          .query({
            query: RESEND_EMAIL_TO_SIGNATORIES,
            variables: {
              documentId: generatedDocumentId,
              documentName: documentName,
              signatoryId: signatureEmployeeId,
              signatoryEmailAddress: emailId,
              signatoryName: signatureEmployeeName,
              isCandidate: isCandidate,
            },
            client: "apolloClientO",
            fetchPolicy: "network-only",
          })
          .then((response) => {
            vm.loadingScreen = false;
            const { errorCode, message } = response.data.resendEmailToSignatory;

            if (!errorCode) {
              let snackbarData = {
                isOpen: true,
                type: "success",
                message: "Email resent successfully to the signatory",
              };
              vm.showAlert(snackbarData);
              // Optionally refetch the list to update the UI
              vm.$emit("refetch-list");
            } else {
              // Handle backend error
              let snackbarData = {
                isOpen: true,
                type: "warning",
                message: message || "Failed to resend email to signatory",
              };
              vm.showAlert(snackbarData);
            }
          })
          .catch((resendErr) => {
            vm.handleResendEmailError(resendErr);
          });
      } catch (error) {
        vm.handleResendEmailError(error);
      }
    },

    // open warning alert before triggering the delete event
    openDeleteConfirmationModal(deleteId) {
      this.deleteEmpDocId = deleteId;
      this.openDeleteConfirmation = true;
    },

    // while closing the delete warning alert
    closeDeleteConfirmationModal() {
      this.openDeleteConfirmation = false;
    },

    // send notification
    sendEmailToEmployee(item) {
      const { employeeEmail } = this.$store.state.userDetails;
      if (employeeEmail) {
        // Ensure we have a valid item object
        if (!item || typeof item !== "object") {
          let snackbarData = {
            isOpen: true,
            type: "error",
            message: "Invalid document data. Please try again.",
          };
          this.showAlert(snackbarData);
          return;
        }

        // Create a clean copy of the item to avoid any reference issues
        this.notifyDocumentDetails = { ...item };
        this.openSendEmailModal = true;
      } else {
        let snackbarData = {
          isOpen: true,
          type: "warning",
          message:
            "Your account is not associated with an email address. Hence you cannot share a document with others.",
        };
        this.showAlert(snackbarData);
      }
    },

    onClickPrint(selectedItem) {
      // Directly trigger browser's print preview (Ctrl+P)
      this.openPrintPreview(selectedItem);
    },

    // Open browser's print preview for the selected document
    async openPrintPreview(selectedItem) {
      try {
        this.loadingScreen = true;

        // Get document content first
        const documentContent = await this.getDocumentContent(selectedItem);

        // Validate that we have proper string content
        if (
          documentContent &&
          typeof documentContent === "string" &&
          documentContent.trim() !== ""
        ) {
          // Create a hidden iframe for printing
          const iframe = document.createElement("iframe");
          iframe.style.position = "absolute";
          iframe.style.top = "-1000px";
          iframe.style.left = "-1000px";
          iframe.style.width = "0";
          iframe.style.height = "0";
          iframe.style.border = "none";

          document.body.appendChild(iframe);

          // Write content to iframe
          const htmlContent = `
            <!DOCTYPE html>
            <html>
            <head>
              <title>Print - ${selectedItem.documentName}</title>
              <style>
                body {
                  font-family: Arial, sans-serif;
                  margin: 20px;
                  line-height: 1.6;
                }
                .document-header {
                  text-align: center;
                  margin-bottom: 30px;
                  border-bottom: 2px solid #ccc;
                  padding-bottom: 20px;
                }
                .document-title {
                  font-size: 24px;
                  font-weight: bold;
                  margin-bottom: 10px;
                }
                .document-info {
                  font-size: 14px;
                  color: #666;
                }
                .document-content {
                  margin-top: 20px;
                }
                @media print {
                  body { margin: 0; }
                  .no-print { display: none; }
                }
              </style>
            </head>
            <body>
              <div class="document-header">
                <div class="document-title">${selectedItem.documentName}</div>
                <div class="document-info">
                  Employee: ${selectedItem.employeeName || "N/A"} |
                  Candidate: ${selectedItem.candidateName || "N/A"} |
                  Status: ${selectedItem.status}
                </div>
              </div>
              <div class="document-content">
                ${documentContent}
              </div>
            </body>
            </html>
          `;

          iframe.contentDocument.open();
          iframe.contentDocument.write(htmlContent);
          iframe.contentDocument.close();

          // Wait for content to load, then trigger print
          iframe.onload = () => {
            iframe.contentWindow.focus();
            iframe.contentWindow.print();

            // Remove iframe after printing
            setTimeout(() => {
              document.body.removeChild(iframe);
            }, 1000);
          };
        } else {
          // Handle case where content is not a valid string
          let fallbackContent = "";
          if (documentContent) {
            if (typeof documentContent === "object") {
              fallbackContent = `<pre>${JSON.stringify(
                documentContent,
                null,
                2
              )}</pre>`;
            } else {
              fallbackContent = `<p>Content type: ${typeof documentContent}</p><pre>${String(
                documentContent
              )}</pre>`;
            }

            // Try to print with fallback content
            this.printWithFallbackContent(selectedItem, fallbackContent);
          } else {
            // Fallback to original modal-based printing if content fetch fails
            this.showAlert({
              isOpen: true,
              type: "warning",
              message:
                "Unable to retrieve document content. Using alternative print method.",
            });
            // Use the original print method as fallback
            this.$emit("action-on-doc-generator", [selectedItem, "print"]);
          }
        }
      } catch (error) {
        this.showAlert({
          isOpen: true,
          type: "error",
          message:
            "An error occurred while preparing the document for printing.",
        });
      } finally {
        this.loadingScreen = false;
      }
    },

    // Print with fallback content when original content has issues
    printWithFallbackContent(selectedItem, fallbackContent) {
      try {
        // Create a hidden iframe for printing
        const iframe = document.createElement("iframe");
        iframe.style.position = "absolute";
        iframe.style.top = "-1000px";
        iframe.style.left = "-1000px";
        iframe.style.width = "0";
        iframe.style.height = "0";
        iframe.style.border = "none";

        document.body.appendChild(iframe);

        // Write content to iframe
        const htmlContent = `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Print - ${selectedItem.documentName}</title>
            <style>
              body {
                font-family: Arial, sans-serif;
                margin: 20px;
                line-height: 1.6;
              }
              .document-header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid #ccc;
                padding-bottom: 20px;
              }
              .document-title {
                font-size: 24px;
                font-weight: bold;
                margin-bottom: 10px;
              }
              .document-info {
                font-size: 14px;
                color: #666;
              }
              .document-content {
                margin-top: 20px;
              }
              pre {
                white-space: pre-wrap;
                word-wrap: break-word;
              }
              @media print {
                body { margin: 0; }
                .no-print { display: none; }
              }
            </style>
          </head>
          <body>
            <div class="document-header">
              <div class="document-title">${selectedItem.documentName}</div>
              <div class="document-info">
                Employee: ${selectedItem.employeeName || "N/A"} |
                Candidate: ${selectedItem.candidateName || "N/A"} |
                Status: ${selectedItem.status}
              </div>
            </div>
            <div class="document-content">
              ${fallbackContent}
            </div>
          </body>
          </html>
        `;

        iframe.contentDocument.open();
        iframe.contentDocument.write(htmlContent);
        iframe.contentDocument.close();

        // Wait for content to load, then trigger print
        iframe.onload = () => {
          iframe.contentWindow.focus();
          iframe.contentWindow.print();

          // Remove iframe after printing
          setTimeout(() => {
            document.body.removeChild(iframe);
          }, 1000);
        };
      } catch (error) {
        // Final fallback to original method
        this.$emit("action-on-doc-generator", [selectedItem, "print"]);
      }
    },

    // Get document content for printing
    async getDocumentContent(selectedItem) {
      try {
        // Check if document has content stored locally first
        if (selectedItem.documentContent) {
          let content = selectedItem.documentContent;

          // Handle case where content might be a Promise
          if (content && typeof content.then === "function") {
            content = await content;
          }

          // Ensure content is a string
          if (typeof content === "string" && content.trim() !== "") {
            return content;
          } else if (typeof content === "object") {
            // If it's an object, try to stringify it
            return JSON.stringify(content);
          }
        }

        // If no local content, fetch from S3 (similar to parent component logic)
        if (selectedItem.documentLink) {
          const response = await this.$apollo.query({
            query: GET_PRESIGNED_URL,
            variables: {
              uploadUrl: selectedItem.documentLink,
              action: "getdata",
            },
            client: "apolloClientP",
            fetchPolicy: "network-only",
          });

          const { s3DocumentDetails, presignedUrl, errorCode } =
            response.data.getPresignedUrl;

          if (!errorCode && s3DocumentDetails) {
            const parsedContent = JSON.parse(s3DocumentDetails);
            return typeof parsedContent === "string"
              ? parsedContent
              : JSON.stringify(parsedContent);
          } else if (!errorCode && presignedUrl) {
            // Fetch content from presigned URL
            const contentResponse = await fetch(presignedUrl);
            return await contentResponse.text();
          }
        }

        return null;
      } catch (error) {
        return null;
      }
    },

    // handle doc template delete error from backend
    handleDeleteError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      this.deleteEmpDocId = null;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It's us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_DB0103": // no delete access
            snackbarData["message"] =
              "Sorry, you don't have access to delete the employee's generated document. Please contact HR administrator.";
            break;
          case "_EC0006": // Record(s) are already deleted in the same or some other user session.
            snackbarData["message"] =
              "Unable to delete the record as it was deleted already in the same or some other user session.";
            break;
          case "_UH0001": //unhandled error
          case "_DB0001": // Error while retrieving the employee access rights
          case "_DB0104": // While check access rights form not found
          case "_DB0002": // Error while checking the employee access rights
          case "CDG0105": // Error while processing the request to delete the generated document.
          case "CDG0006": // Error while deleting the generated document.
          case "_EC0007": // Invalid input field(s).
          default:
            snackbarData["message"] =
              "Something went wrong while deleting the employee's generated document. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while deleting the employee's generated document. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    // handle resend email error
    handleResendEmailError(err = "") {
      let snackbarData = {
        isOpen: true,
        type: "warning",
        message: "",
      };
      this.loadingScreen = false;
      // check if it is a graphql error
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodes(err);
        switch (errorCode) {
          case "_DB0000": // technical issues.
            snackbarData["message"] =
              "It's us! There seems to be some technical difficulties. Please try after some time.";
            break;
          case "_EC0007": // Invalid input field(s).
          case "_UH0001": // unhandled error
          case "PBP0105": // Error in sending email
          case "CDG0123": // Error while processing the request to resend the signature link to the signatory.
          case "CDG0016": // Error while resending the signature link to the signatory.
          default:
            snackbarData["message"] =
              "Something went wrong while resending the document to signatory. If you continue to see this issue, please contact the platform administrator.";
            break;
        }
      } else if (err && err.networkError) {
        snackbarData["message"] = handleNetworkErrors(err);
      } else {
        snackbarData["message"] =
          "Something went wrong while resending the document to signatory. Please try after some time.";
      }
      this.showAlert(snackbarData);
    },

    checkIfSignedByAll(data) {
      if (!data || !Array.isArray(data) || data.length === 0) {
        return false;
      }

      const nonCandidates = data.filter(
        (item) => item?.signatureKey?.toLowerCase() !== "candidate"
      );

      if (nonCandidates.length === 0) {
        return true; // If there are no non-candidates, consider it as all signed
      }

      const allNonCandidatesSigned = nonCandidates.every(
        (item) => item?.status?.toLowerCase() === "signed"
      );
      return allNonCandidatesSigned;
    },

    // show the message in snack bar
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
  },
};
</script>

<style scoped>
:deep(.v-pagination) {
  background-color: transparent !important;
}
:deep(.v-pagination__item) {
  background-color: transparent !important;
}
.data-table-side-border {
  margin-left: -1.1em;
  margin-right: 10px;
  min-height: 4em;
  border-top-left-radius: 7px;
  border-bottom-left-radius: 7px;
}
</style>
