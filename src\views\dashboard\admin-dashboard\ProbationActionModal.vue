<template>
  <div>
    <v-dialog
      v-model="openProbationModal"
      class="custom-modal-radius"
      width="500px"
      min-height="400px"
      scrollable
      @click:outside="closeProbationModal()"
    >
      <v-card class="custom-modal-radius">
        <div class="d-flex justify-end">
          <v-icon
            id="probation-modal-close"
            color="primary"
            variant="tonal"
            class="pr-3 pt-3"
            @click="closeProbationModal()"
          >
            fas fa-times
          </v-icon>
        </div>
        <v-card-text class="mt-n4">
          <v-row class="px-6" align="center">
            <v-col cols="12" class="text-center">
              <v-avatar
                class="text-xs-center justify-center avatarClass avatar_property"
                :class="['avatarBCColor' + (selectedProbation.employeeId % 5)]"
                size="50"
              >
                <span style="font-size: 15px">
                  {{ letterAvatar(selectedProbation.employeeName) }}
                </span>
              </v-avatar>
              <div class="text-body-1 text-primary font-weight-bold">
                {{ selectedProbation.employeeName }}
                <div class="text-grey">
                  {{ selectedProbation.userDefinedEmployeeId }}
                </div>
              </div>
            </v-col>
            <v-col cols="12">
              <v-btn-toggle
                v-model="selectedProbationType"
                color="primary"
                mandatory
                rounded="lg"
                variant="outlined"
              >
                <v-btn
                  value="Extend Probation Period"
                  size="small"
                  class="text-none"
                >
                  {{ $t("dashboard.extendProbation") }}
                </v-btn>
                <v-btn value="Confirm Employee" size="small" class="text-none">
                  {{ $t("dashboard.confirmEmployee") }}
                </v-btn>
              </v-btn-toggle>
            </v-col>
            <v-col cols="12">
              <v-menu
                v-model="dateMenu"
                :close-on-content-click="false"
                transition="scale-transition"
                offset-y
                min-width="auto"
              >
                <template v-slot:activator="{ props }">
                  <v-text-field
                    v-model="formattedProbDate"
                    prepend-inner-icon="fas fa-calendar"
                    readonly
                    v-bind="props"
                    variant="solo"
                  >
                    <template v-slot:label>
                      {{ $t("dashboard.probationDate") }}
                    </template>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="selectedProbationDate"
                  :min="probationMinDate"
                />
              </v-menu>
            </v-col>
            <v-col
              v-if="selectedProbationType === 'Confirm Employee'"
              class="mt-n4"
              cols="6"
            >
              <v-label>{{ $t("dashboard.confirmationDate") }}</v-label>

              <v-tooltip
                location="top"
                text="To change the confirmation date, please update the probation date(Next date of the probation date)."
                max-width="400"
              >
                <template v-slot:activator="{ props }">
                  <span v-bind="props" class="pa-1 cursor-not-allowed">
                    <v-icon
                      size="x-small"
                      color="info"
                      class="fas fa-info-circle"
                    ></v-icon>
                  </span>
                </template>
              </v-tooltip>
            </v-col>
            <v-col
              v-if="selectedProbationType === 'Confirm Employee'"
              class="mt-n4 text-body-1 text-primary"
              cols="6"
            >
              {{ confirmationDate("DD MMM, YYYY") }}
            </v-col>
          </v-row>
        </v-card-text>
        <div class="text-center mb-6">
          <v-btn
            color="primary"
            rounded="lg"
            variant="elevated"
            @click="updateProbation()"
          >
            {{ $t("common.submit") }}
          </v-btn>
        </div>
      </v-card>
    </v-dialog>
    <AppLoading v-if="showLoadingScreen" />
  </div>
</template>

<script>
import moment from "moment";
import { UPDATE_PROBATION } from "@/graphql/dashboard/dashboardQueries";
import { getErrorCodesWithValidation, handleNetworkErrors } from "@/helper";
export default {
  name: "ProbationActionModal",

  props: {
    selectedProbation: {
      type: Object,
      required: true,
    },
    openModal: {
      type: Boolean,
      required: true,
    },
  },
  emits: ["close-modal", "update-success"],

  data: () => ({
    dateMenu: false,
    selectedProbationDate: "",
    probationMinDate: "",
    openProbationModal: true,
    formattedProbDate: "",
    showLoadingScreen: false,
    selectedProbationType: "Extend Probation Period",
  }),

  computed: {
    // format date using organization date format
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat) : "";
        }
      };
    },
  },

  watch: {
    selectedProbationDate(val) {
      if (val) {
        this.dateMenu = false;
        this.formattedProbDate = this.formatDate(val);
      }
    },
  },

  mounted() {
    this.openProbationModal = this.openModal;
    const { probationDate, probationDays, dateOfJoin } = this.selectedProbation;
    let probDate = "";
    // check probation date is available or not
    if (probationDate) {
      probDate = probationDate;
    }
    // if probation date is not available, then check dateOfJoin availability
    else if (dateOfJoin) {
      // check probation days is 0, then consider dateOfJoin as probation date
      if (!probationDays) {
        probDate = dateOfJoin;
      }
      // if probations days is greater than 0, then add with dateOfJoin and consider as probation date
      else {
        probDate = moment(dateOfJoin, "YYYY-MM-DD")
          .add(parseInt(probationDays, 10), "days")
          .format("YYYY-MM-DD");
      }
    }
    this.selectedProbationDate = probDate;
    // set dateOfJoin as probation date-picker's minimum date
    this.probationMinDate = dateOfJoin;
  },

  methods: {
    // add one day to probation date to calculate confirmation date
    confirmationDate(format) {
      return moment(this.selectedProbationDate, "YYYY-MM-DD")
        .add(1, "days")
        .format(format);
    },
    // close probation modal
    closeProbationModal() {
      this.selectedProbationDate = "";
      this.$emit("close-modal");
    },
    // update probation date
    updateProbation() {
      let vm = this;
      vm.showLoadingScreen = true;
      try {
        const { employeeId, dateOfJoin, probationDate } = vm.selectedProbation;
        vm.$apollo
          .mutate({
            mutation: UPDATE_PROBATION,
            variables: {
              employeeId: employeeId,
              dateOfJoin: dateOfJoin,
              oldProbationDate: probationDate,
              newProbationDate: moment(vm.selectedProbationDate).isValid()
                ? moment(vm.selectedProbationDate).format("YYYY-MM-DD")
                : "",
              confirmationDate:
                vm.selectedProbationType === "Confirm Employee"
                  ? vm.confirmationDate("YYYY-MM-DD")
                  : "",
            },
            client: "apolloClientM",
          })
          .then((res) => {
            const { errorCode } = res.data.updateProbationAndConfirmation;
            if (!errorCode) {
              if (vm.selectedProbationType === "Confirm Employee") {
                let snackbarData = {
                  isOpen: true,
                  message: "Employee confirmed successfully!",
                  type: "success",
                };
                vm.showAlert(snackbarData);
              } else {
                let snackbarData = {
                  isOpen: true,
                  message: "Probation extended successfully!",
                  type: "success",
                };
                vm.showAlert(snackbarData);
              }
              vm.showLoadingScreen = false;
              vm.selectedProbationDate = "";
              vm.$emit("update-success");
            } else {
              vm.handleProbationUpdateError();
            }
          })
          .catch((updateError) => {
            vm.handleProbationUpdateError(updateError);
          });
      } catch (err) {
        vm.handleProbationUpdateError(err);
      }
    },
    // handle probation update actions error
    handleProbationUpdateError(err = "") {
      let snackbarData = {
        isOpen: true,
        message: "Probation updated successfully!",
        type: "warning",
      };
      if (err && err.graphQLErrors) {
        let errorCode = getErrorCodesWithValidation(err);
        if (errorCode) {
          switch (errorCode[0]) {
            case "_DB0000": // technical errors
              snackbarData.message =
                "It’s us! There seems to be some technical difficulties. Please try after some time.";
              break;
            case "EE00003": // Employee probation date or confirmation date are updated. But the leave balance is not updated.
              snackbarData.message =
                "Employee probation is updated. But unable to update the leave balance.";
              break;
            case "EE00005": // Probation date or confirmation date are updated already.
              snackbarData.message =
                "Probation was already updated in the same or another user session.";
              break;
            // if any input validation error occurs, BAD_USER_INPUT was returned as error code from backend
            case "BAD_USER_INPUT":
              var validationErrors = errorCode[1]; // validation error json
              // array of validation error codes
              var validationErrorArray = validationErrors
                ? Object.keys(validationErrors)
                : [];
              // add all the backend validation error messages as single sentence to present it to the users
              var validationMessages = "";
              // check validation json exist with json keys length
              if (validationErrorArray.length > 0) {
                for (var i in validationErrorArray) {
                  if (
                    validationErrorArray[i] === "IVE0208" // Probation date is required.
                  ) {
                    validationMessages =
                      validationMessages +
                      " " +
                      validationErrors[validationErrorArray[i]];
                  }
                }
              }
              // if validation messages are available, we have to present that error messages
              if (validationMessages) {
                snackbarData["message"] = validationMessages;
              }
              // other errors not handled by users. so as of now it was considered as common error
              else {
                // "IVE0083": Please provide a employee id.
                // "IVE0209": Date of join is required
                // "IVE0210": old probation date is required
                snackbarData["message"] =
                  "Something went wrong while updating the probation. Please try after some time.";
              }
              break;
            // functional errors
            case "EE00102": // Something went wrong! Please contact the system admin.
            case "EE00002": // Error while updating the employee probation.
            case "EE00004": // Error occurred while updating the employee leave balance.
            case "_UH0001": // unhandled error
            default:
              snackbarData.message =
                "Something went wrong while updating the probation. If you continue to see this issue, please contact the platform administrator.";
              break;
          }
        } else {
          snackbarData.message =
            "Something went wrong while updating the probation. Please try after some time.";
        }
      } else if (err && err.networkError) {
        snackbarData.message = handleNetworkErrors(err);
      } else {
        snackbarData.message =
          "Something went wrong while updating the probation. Please try after some time.";
      }
      this.showAlert(snackbarData);
      this.showLoadingScreen = false;
      this.closeProbationModal();
    },
    // show error or success messages inside alert
    showAlert(snackbarData) {
      this.$store.commit("OPEN_SNACKBAR", snackbarData);
    },
    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },
  },
};
</script>
