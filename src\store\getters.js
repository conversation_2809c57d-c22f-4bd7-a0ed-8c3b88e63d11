export default {
  //to get base url of the app
  baseUrl: (state, getters) => {
    if (getters.isLocalEnv) {
      let pathParts = location.pathname.split("/");
      return location.origin + "/" + pathParts[1].trim("/") + "/"; // http://localhost/hrapponline/
    } else {
      return location.origin + "/"; // http://subdomain.hrapp.co/
    }
  },

  isLocalEnv: () => {
    let pathParts = location.pathname.split("/");
    let currentUrl = window.location.href;
    if (
      parseInt(localStorage.getItem("isProduction"), 10) === 0 ||
      pathParts.includes("hrapponline") ||
      currentUrl.includes("localhost")
    ) {
      return true;
    } else {
      return false;
    }
  },

  //to get orgCode dynamically from the current url
  orgCode: (state, getters) => {
    if (getters.isLocalEnv) {
      return "capricetest"; // local db connection
    } else {
      let url = window.location.href;
      let urlNoProtocol = url.replace(/^https?:\/\//i, "");
      let orgCode = urlNoProtocol.split(".");
      orgCode = orgCode[0];
      return orgCode;
    }
  },

  mixPanelId: (state, getters) => {
    let oCode = localStorage.getItem("orgCode");
    oCode = oCode ? oCode : getters.orgCode;
    let eId = state.orgDetails.employeeId;
    let eName = state.userDetails.employeeFullName;
    return oCode + "-" + eName + "-" + eId;
  },

  //to get domain dynamically from the current url
  domain: (state, getters) => {
    if (getters.isLocalEnv) {
      return "hrapp"; // local domain name
    } else {
      let url = window.location.href;
      let urlNoProtocol = url.replace(/^https?:\/\//i, "");
      let domain = urlNoProtocol.split(".");
      domain = domain[1];
      return domain;
    }
  },

  //to get accessRights for the particular form
  //if formName not sent then return all forms with rights
  formAccessRights: (state) => (formName) => {
    if (state.formAccessRights && formName) {
      let accessRights = JSON.parse(state.formAccessRights);
      accessRights = accessRights[`${formName}`]
        ? accessRights[`${formName}`]
        : ""; // check access rights exists for that form and return
      return accessRights;
    }
    // if form access rights from BE is not retrieved, we can return empty object
    return null;
  },

  formIdBasedAccessRights: (state) => (formId) => {
    if (state.formIdAccessRights && formId) {
      let accessRights = state.formIdAccessRights;
      accessRights = accessRights[`${formId}`] ? accessRights[`${formId}`] : ""; // check access rights exists for that form and return
      return accessRights;
    }
    // if form access rights from BE is not retrieved, we can return empty object
    return null;
  },
  redirectionFormUrl: (state) => (moduleName) => {
    if (state.sideBarMenuList && moduleName) {
      let sidebarMenu = state.sideBarMenuList;
      sidebarMenu = sidebarMenu.filter((el) => el.moduleName === moduleName);
      return sidebarMenu;
    } else {
      let sidebarMenu = state.sideBarMenuList;
      return sidebarMenu;
    }
  },

  // function to check the login employee is any one of the admin
  checkAndReturnOneOfAdminAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let adminForm of state.adminForms) {
      let formName = adminForm.replace(/\s/g, "-");
      formName = formName.toLowerCase();
      var formRights = getters.formAccessRights(formName);
      if (
        formRights &&
        formRights.accessRights &&
        formRights.accessRights["update"]
      ) {
        formAccess[`${adminForm}`] = true;
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[`${adminForm}`] = false;
      }
    }
    return {
      access: formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  formIdsBasedAccessRights: (state) => (formIds) => {
    if (
      !state.formIdAccessRights ||
      !Array.isArray(formIds) ||
      formIds.length === 0
    ) {
      return [];
    }

    return formIds
      .map((formId) => {
        const accessRights = state.formIdAccessRights[formId];
        if (accessRights) {
          return {
            formId,
            formName: accessRights.formName || "",
            customFormName: accessRights.customFormName || "",
            parentFormId: accessRights.parentFormId || 0,
          };
        }
        return null;
      })
      .filter((item) => item !== null);
  },

  //To get organization name of the employee
  organizationName(state) {
    const { organizationName } = state.orgDetails;
    if (organizationName) {
      return organizationName;
    } else {
      return "";
    }
  },

  // get snackbar store data
  getSnackbarData(state) {
    let snackbarData = {
      isOpen: state.showSnackbar,
      message: state.snackBarMessage,
      type: state.snackBarType,
    };
    return snackbarData;
  },

  //check whether form is in vue 3 layout
  checkForVue3LayoutForms: (state) => (formId) => {
    const { vue3Forms } = state;
    if (vue3Forms.length > 0) {
      let formsInVueLayout = vue3Forms.filter((form) => form.formId === formId);
      return formsInVueLayout.length > 0;
    } else {
      return false;
    }
  },

  checkForVue2LayoutForms: (state) => (formName) => {
    const { vue2Forms } = state;
    if (vue2Forms.length > 0) {
      let formsInVueLayout = vue2Forms.filter((form) => form === formName);
      return formsInVueLayout.length > 0;
    } else {
      return false;
    }
  },

  checkForPartnerLayoutForms: (state) => (formName) => {
    const { partnerForms } = state;
    if (partnerForms.length > 0) {
      let formsInPartnerLayout = partnerForms.filter(
        (form) => form === formName
      );
      return formsInPartnerLayout.length > 0;
    } else {
      return false;
    }
  },

  // function to return all the emp-data-setup form access
  empDataSetupFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.employeeDataSetupForms) {
      var formRights = getters.formAccessRights(empDataSetupForm.formName);
      var dFormName = empDataSetupForm.displayFormName;
      dFormName =
        dFormName === "Projects" ? state.projectLabel + "s" : dFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  empDataManagementTabs: (state, getters) => {
    let formAccess = [],
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.empDataManagementForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      if (formRights && formRights.accessRights["view"]) {
        formAccess.push({
          ...empDataSetupForm,
          havingAccess: true,
        });
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess.push({
          ...empDataSetupForm,
          havingAccess: false,
        });
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },

  orgStructureFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.coreHrOrgStructureForms) {
      var formRights = getters.formAccessRights(empDataSetupForm.formName);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  settingsGeneralFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.settingsGeneralForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  settingsRecruitmentFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.settingsRecruitmentForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  myTeamTimeOffFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.myTeamTimeOffForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  selfServiceTimeOffFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.selfServiveTimeOffForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  coreHrPayrollDataManagementFormAcess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.coreHrPayrollDataManagementForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  employeeAttendanceTabs: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.employeeAttendanceForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  attendanceFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.myTeamAttendanceForms) {
      var formRights = getters.formIdBasedAccessRights(empDataSetupForm.formId);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  payrollReconciliationFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.PayrollReconciliationForm) {
      var formRights = getters.formAccessRights(empDataSetupForm.formName);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  // access check for roster management
  rosterManagementFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.rosterManagementForm) {
      var formRights = getters.formAccessRights(empDataSetupForm.formName);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  shiftSwapFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let empDataSetupForm of state.shiftSwapForm) {
      var formRights = getters.formAccessRights(empDataSetupForm.formName);
      var dFormName = empDataSetupForm.displayFormName;
      if (formRights && formRights.accessRights["view"]) {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess[dFormName] = {
          ...empDataSetupForm,
          havingAccess: false,
        };
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },

  lossPreventionFormAccess: (state, getters) => {
    let formAccess = [], // change form object to array
      isAnyOneFormHaveAccess = false;
    for (let dataLossForm of state.dataLossPreventionForm) {
      var formRights = getters.formIdBasedAccessRights(dataLossForm.formId);
      if (formRights && formRights.accessRights["view"]) {
        formAccess.push({
          ...dataLossForm,
          havingAccess: true,
        }); // instead of assignment push in array
        isAnyOneFormHaveAccess = true;
      } else {
        formAccess.push({
          ...dataLossForm,
          havingAccess: false,
        }); // instead of assignment push in array
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },

  //to give the table height
  getTableHeight: () => (removeHeight) => {
    let systemBarActiveHeight = document.getElementById("systembar") ? 60 : 0;
    let calculatedHeight = systemBarActiveHeight + removeHeight;
    return `calc(100vh - ${calculatedHeight}px)`;
  },

  getTableHeightBasedOnScreenSize:
    (state) => (removeHeight, itemList, isTwoRowInTop) => {
      let systemBarActiveHeight = document.getElementById("systembar") ? 60 : 0;
      let calculatedHeight = systemBarActiveHeight + removeHeight;
      if (state.isMobileWindowSize) {
        let mHeight = isTwoRowInTop ? 110 : 60;
        calculatedHeight = calculatedHeight + mHeight;
        return `calc(100vh - ${calculatedHeight}px)`;
      } else if (state.windowWidth <= 960) {
        calculatedHeight = calculatedHeight + 50;
        return `calc(100vh - ${calculatedHeight}px)`;
      } else if (itemList && itemList.length > 10) {
        return `calc(100vh - ${calculatedHeight}px)`;
      } else {
        return "";
      }
    },

  checkAnyOneOfTheAdmin: (state, getters) => {
    let isAnyOneFormHaveAccess = false;
    for (let adminForm of state.adminForms) {
      let formName = adminForm.replace(/\s/g, "-");
      formName = formName.toLowerCase();
      var formRights = getters.formAccessRights(formName);
      if (formRights && formRights.accessRights["update"]) {
        isAnyOneFormHaveAccess = true;
        break;
      }
    }
    return isAnyOneFormHaveAccess;
  },

  // Check if Entomo integration is enabled
  entomoIntegrationEnabled: () => {
    const partnerid = window.$cookies.get("partnerid")
      ? window.$cookies.get("partnerid").trim()
      : "";
    return partnerid?.toLowerCase() === "entomo";
  },

  coreHrSettingsFormAccess: (state, getters) => {
    let formAccess = {},
      isAnyOneFormHaveAccess = false;
    for (let corHrFrom of state.coreHrSettingsForms) {
      var formRights = corHrFrom.formId
        ? getters.formIdBasedAccessRights(corHrFrom.formId)
        : getters.formAccessRights(corHrFrom.formName);
      let accessType = corHrFrom.accessType ? corHrFrom.accessType : "view";
      if (formRights && formRights.accessRights[accessType]) {
        formAccess[corHrFrom.displayFormName] = {
          ...corHrFrom,
          havingAccess: true,
        };
        isAnyOneFormHaveAccess = true;
      } else {
        let subFormHaveAccess = false;
        if (corHrFrom.subForms && corHrFrom.subForms.length > 0) {
          for (let subForm of corHrFrom.subForms) {
            var subFormRights = getters.formAccessRights(subForm);
            if (subFormRights && subFormRights.accessRights["view"]) {
              formAccess[corHrFrom.displayFormName] = {
                ...corHrFrom,
                havingAccess: true,
              };
              isAnyOneFormHaveAccess = true;
              subFormHaveAccess = true;
              break;
            }
          }
        }
        if (!subFormHaveAccess) {
          formAccess[corHrFrom.displayFormName] = {
            ...corHrFrom,
            havingAccess: false,
          };
        }
      }
    }
    return {
      formAccess,
      isAnyOneFormHaveAccess: isAnyOneFormHaveAccess,
    };
  },
  coreHrTimeOffTabs: (state, getters) => {
    let formsWithAccess = [],
      isAnyOneFormHaveAccess = false;
    for (let form of state.coreHrTimeOffForms) {
      var formRights = getters.formIdBasedAccessRights(form.formId);
      if (formRights && formRights.accessRights["view"]) {
        formsWithAccess.push({
          ...form,
          ...formRights,
          havingAccess: true,
        });
        isAnyOneFormHaveAccess = true;
      } else {
        formsWithAccess.push({
          ...form,
          ...formRights,
          havingAccess: false,
        });
      }
    }
    return { formsWithAccess, isAnyOneFormHaveAccess };
  },
  statutoryComponentsTabs: (state, getters) => {
    const statutoryComponents = [
      {
        name: "Provident Fund",
        url: "provident-fund",
        formIds: [52, 259, 71],
        primaryFormId: 52,
        isPhp: false,
      },
      {
        name: "NPS",
        url: "nps",
        formIds: [126, 260, 127],
        primaryFormId: 126,
        isPhp: false,
      },
      {
        name: "Insurance",
        url: "insurance",
        formIds: [378],
        isPhp: false,
      },
      {
        name: "Gratuity",
        url: "organization/organization-settings",
        formIds: [110],
        primaryFormId: 110,
        isPhp: true,
      },
      {
        name: "Professional Tax",
        url: "payroll/tax-rules",
        formIds: [90],
        primaryFormId: 90,
        isPhp: true,
      },
      {
        name: "Labour Welfare Fund",
        url: "payroll/labour-welfare-fund",
        formIds: [150],
        primaryFormId: 150,
        isPhp: true,
      },
    ];
    const hasViewAccess = (formIds) => {
      return formIds.some((formId) => {
        const form = getters.formIdBasedAccessRights(formId);
        return form?.accessRights?.view;
      });
    };
    const getCustomFormName = (primaryFormId, defaultName) => {
      const form = getters.formIdBasedAccessRights(primaryFormId);
      return form?.customFormName || defaultName;
    };
    const formsWithAccess = statutoryComponents.map((component) => {
      const havingAccess = hasViewAccess(component.formIds);
      return {
        formName: getCustomFormName(component.primaryFormId, component.name),
        url: component.url,
        havingAccess,
        isPhp: component.isPhp,
      };
    });
    const isAnyOneFormHaveAccess = formsWithAccess.some(
      (form) => form.havingAccess
    );
    return { formsWithAccess, isAnyOneFormHaveAccess };
  },
  myDeclarationTabs: (state, getters) => {
    const myDeclarationForms = [
      {
        name: "FBP Declaration",
        url: "fbp-declaration",
        formId: 388,
      },
      {
        name: "NPS Declaration",
        url: "nps-declaration",
        formId: 389,
      },
    ];
    const getCustomFormName = (formId, defaultName) => {
      const form = getters.formIdBasedAccessRights(formId);
      return form?.customFormName || defaultName;
    };
    const formsWithAccess = myDeclarationForms.map((component) => {
      const havingAccess = getters.getFormAccess(component.formId, "view");
      return {
        formId: component.formId,
        formName: getCustomFormName(component.formId, component.name),
        url: component.url,
        havingAccess,
      };
    });
    const isAnyOneFormHaveAccess = formsWithAccess.some(
      (form) => form.havingAccess
    );
    return { formsWithAccess, isAnyOneFormHaveAccess };
  },

  getFormAccess: (state, getters) => (formId, action) => {
    let formAccess = getters.formIdBasedAccessRights(formId);
    if (
      formAccess &&
      formAccess.accessRights &&
      formAccess.accessRights[action]
    ) {
      return true;
    } else {
      return false;
    }
  },
};
