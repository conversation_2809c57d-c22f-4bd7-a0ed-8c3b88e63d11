export default {
  en: {
    /* ----- Settings ----- */
    // Module names
    settings: "Settings",

    // Payroll
    payroll: "Payroll",
    generalTab: "General",
    salaryComponents: "Salary Components",
    salaryTemplate: "Salary Template",
    earnings: "Earnings",
    reimbursements: "Reimbursements",
    bonus: "Bonus",
    addEarnings: "Add Earning",
    addReimbursements: "Add Reimbursement",
    addBonus: "Add Bonus",
    viewEarnings: "View Earning",
    viewReimbursements: "View Reimbursement",
    viewBonus: "View Bonus",
    editEarnings: "Edit Earning",
    editReimbursements: "Edit Reimbursement",
    editBonus: "Edit Bonus",
    earningName: "Earning Name",
    reimbursementName: "Reimbursement Name",
    bonusName: "Bonus Name",
    taxInclusion: "Tax Inclusion",
    fbpComponent: "Flexible Benefit Plan Component",
    period: "Period",
    perks: "Perks",
    description: "Description",
    considerForEPF: "Consider For Epf Contribution",
    calculationType: "Calculation Type",
    percentage: "Percentage",
    maxLimit: "FBP Max Declaration Amount",
    asIsPayment: "As Is Payment",
    reimbursementType: "Reimbursement Type",
    workFlow: "Workflow",
    restrictEmployeeFromOverridingFBPAmount:
      "Restrict employee from overriding the FBP amount",
    benefitsAssociation: "Benefits Association",
    considerForProvidentFund: "Consider for Provident Fund",
    considerForInsurance: "Consider for Insurance",
    // Core HR
    timeOff: "Time Off",
    compOff: "Comp Off",
    specialWages: "Special Wages",
    overtime: "Overtime",
    attendanceConfiguration: "Attendance Configuration",
    geoFencing: "Geo-Fencing & Selfie Attendance",
    holidays: "Holidays",
    preApprovals: "Pre Approvals",
    lopRecovery: "LOP Recovery",
    roster: "Roster",
    roles: "Roles",
    employeeShare: "Employee Share",
    employerShare: "Employer Share",
    employeeShareAmount: "Employee Share Amount",
    employerShareAmount: "Employer Share Amount",
    monthly: "Monthly",
    annually: "Annually",

    // Data Loss Prevention Settings
    dataLossPrevention: "Data Loss Prevention",
    internetAccessControl: "Internet Access Control",
    locationTracking: "Location Tracking",
    keyLogging: "Key Logging",
    additionalScreenshots: "Additional Screenshots",
    auditLog: "Audit Log",

    // Productivity Monitoring
    members: "Members",
    reports: "Reports",
    productivityMonitoring: "Productivity Monitoring",

    // Common
    status: "Status",
    enabled: "Enabled",
    disabled: "Disabled",
    enable: "Enable",
    disable: "Disable",
    active: "Active",
    inactive: "Inactive",
    new: "New",
    added: "Added",
    updated: "Updated",
    exitFormWarning: "Are you sure to exit this form?",
    noChangesToUpdate: "There are no changes to be updated",
    confirmExitForm: "Are you sure to exit this form?",
    noRecordsFound:
      "There are no {formName} for the selected filters/searches.",
    ipAddress: "IP Address",
    cidr: "CIDR",
    employee: "Employee",
    employeeName: "Employee Name",
    employeeId: "Employee ID",
    organization: "Organization",
    organizationLevel: "Organization Level",
    employeeLevel: "Employee Level",
    addedOn: "Added On",
    updatedOn: "Updated On",
    addedBy: "Added By",
    updatedBy: "Updated By",
    actions: "Actions",
    yes: "Yes",
    no: "No",
    save: "Save",
    cancel: "Cancel",
    close: "Close",
    edit: "Edit",
    delete: "Delete",
    add: "Add",
    addNew: "Add New",
    search: "Search",
    filter: "Filter",
    resetFilter: "Reset Filter",
    resetFilterSearch: "Reset Filter/Search",
    export: "Export",
    refresh: "Refresh",
    setAll: "Set all",
    retry: "Retry",
    changedSettings: "You have changed {count} setting(s)",
    addConfiguration: "Add Configuration",
    itemPerPage: "Items per page:",
    pageText: "{start}-{end} of {total}",
    apply: "Apply",
    reset: "Reset",
    designation: "Designation",
    department: "Department",

    // Internet Access Control
    enableInternetAccessControl: "Enable Internet Access Control",
    notifyInternetAccessViolation: "Notify Internet Access Violation",
    internetAccessUpdateFrequency: "Internet Access Update Frequency",
    overrideInternetAccess: "Override Internet Access",
    blockedDomains: "Blocked Domains",
    whitelistedDomains: "Whitelisted Domains",
    domainName: "Domain Name",
    category: "Category",
    productive: "Productive",
    unproductive: "Unproductive",
    neutral: "Neutral",
    addBlockedDomain: "Add Blocked Domain",
    addWhitelistedDomain: "Add Whitelisted Domain",
    domainCategory: "Domain Category",
    tooltipDomainContent:
      "Admin can override the organization level internet access control with an employee level access control by editing the configuration below",
    notifyButtonContent:
      "When Internet access control is set to 'No,' please note that notify internet access will be set to 'No' and cannot be modified.",
    accessDeniedUpdateMessage: "Login employee does not have an update access",
    saveOrCancelChanges: "Please save or cancel the changes before proceeding",
    enableInternetAccessMessage:
      "Please enable the internet access control feature to allow the addition or removal of websites from the blocked list.",

    // Internet Access Control Modal
    websitesBlockedUnblocked: "Websites (Blocked/Unblocked)",
    appsBlockedUnblocked: "Apps (Blocked/Unblocked)",
    customGroup: "Custom Group",
    selectCustomGroup: "Select Custom Group",
    copyClassificationFrom: "Copy the classification from",
    copyNow: "Copy Now",
    wildcardConfiguration: "Wildcard Configuration",
    blockAccess: "Block access",
    back: "Back",
    followingWebsitesBlocked: "Following websites will be blocked for access.",
    noSuchDomain: "No such domain exists, or domain is already blocked.",
    thereAreNoBlockedWebsites: "There are no blocked websites",
    addMore: "Add More",
    changesHaveBeenDone: "{count} change(s) has been done",
    domainRemovedSuccess: "Domain removed from blocklist successfully.",
    domainStatusUpdatedSuccess: "Domain status updated successfully",
    domainAddedSuccess: "Domain successfully added to the blocked list.",
    clearAll: "Clear All",
    all: "All",
    blocked: "Blocked",
    unblocked: "Unblocked",
    block: "Block",
    unblock: "Unblock",
    domainStatusCannotBeChanged:
      "Domain status cannot be changed as it was set through wildcard configuration",
    noItemsToCategorizePre: "You do not have any ",
    noItemsToCategorizePost: " to categorize.",
    moreDetails: "More Details",

    // Location Tracking
    locationTrackingSettings: "Location Tracking Settings",
    enableLocationTracking: "Enable Location Tracking",
    workLocation: "Work Location",
    ipRange: "IP Range",
    addWorkLocation: "Add Work Location",
    editWorkLocation: "Edit Work Location",
    locationName: "Location Name",
    locationTrackingSettingsUpdatedSuccess:
      "Location tracking settings updated successfully",
    workLocationAddedSuccess: "Work Location added successfully",
    workLocationUpdatedSuccess: "Work Location updated successfully",
    locationTrackingDescription:
      "Track employee locations based on IP addresses",
    ipRangeFormat: "IP Range Format (e.g., ***********-*************)",
    locationTrackingLongDescription:
      "To ensure accurate categorization of user activity in the location dashboard, please assign a specific range of IP addresses to your office network.",
    ipAddressTypePublic: "IP Address Type: Public (Internet IP)",
    addressFormatIPv4: "Address Format: IPv4 only",
    ipRangeSpecify:
      "Range: Specify the IP address or range (including subnet mask)",
    ipAddressImportantNote:
      "Important: IP addresses assigned to past activities will not be updated retrospectively.",
    locationTrackingNote1:
      "Location Tracking settings can help oversee and manage network usage by logging the IP addresses of devices connected to your company's network",
    locationTrackingNote2:
      "The settings typically include options for enabling or disabling tracking, adding new work locations and IP addresses",

    // Key Logging
    keyLoggingSettings: "Key Logging Settings",

    captureControlKeys: "Capture Control Keys",
    keyStrokeCount: "Key Stroke Count",
    applicationName: "Application Name",
    websites: "Websites",
    windowTitle: "Window Title",

    // Additional Screenshots
    additionalScreenshotsSettings: "Additional Screenshots Settings",

    captureScreenshot: "Capture Screenshot",
    screenshotSchedule: "Screenshot Schedule",
    fixed: "Fixed",
    random: "Random",
    screenshotFrequency: "Screenshot Frequency",
    screenshotFrequencyType: "Screenshot Frequency Type",
    screenshotsPerFrequency: "Screenshots Per Frequency",
    additionalScreenshotsPerFrequency: "Additional Screenshots Per Frequency",
    screenshotBlur: "Screenshot Blur",
    minutes: "Minutes",
    hours: "Hours",

    // Messages
    confirmationHeading: "Are you sure to change the status?",
    settingsUpdatedSuccess: "Settings updated successfully",
    locationTrackingUpdatedSuccess:
      "Location tracking settings updated successfully",

    keyLoggingUpdatedSuccess: "Key Logging settings updated successfully",
    screenshotsUpdatedSuccess: "Screenshots settings updated successfully",
    accessDeniedMessage: "You don't have access to perform this action",

    // Help text
    internetAccessControlHelp:
      "Internet Access Control helps monitor and control access to websites and domains across your organization.",
    locationTrackingHelp:
      "Location Tracking helps identify whether employees are working from office or remote locations based on IP addresses.",
    keyLoggingHelp:
      "Key Logging tracks keyboard activity to monitor productivity and detect potential data leaks.",
    keyLoggingDescription:
      "Enabling key logging will capture all keystrokes made by users while they interact with the application, helping monitor user activity. This data can provide insights into usage patterns and ensure compliance with security policies.",
    additionalScreenshotsHelp:
      "Additional Screenshots allows capturing extra screenshots to monitor employee activity beyond regular intervals.",
    additionalScreenshotsNote1:
      "Periodic or triggered screenshots are captured to give visual context to the logs, helping to verify the actual tasks being performed.",
    additionalScreenshotsNote2:
      "Takes automatic screenshots at intervals or based on specific triggers (e.g., opening specific applications or websites).",

    // Employee Number Series
    employeeNumberSeries: "Employee Number Series",
    employeeNumberSeriesSettings: "Employee Number Series Settings",
    enableEmployeeNumberSeries: "Enable Employee Number Series",
    prefix: "Prefix",
    suffix: "Suffix",
    noOfDigits: "Number of Digits",
    nextNumber: "Next Number",
    viewEmployeeNumberSeries: "View Employee Number Series",
    addEmployeeNumberSeries: "Add Employee Number Series",
    editEmployeeNumberSeries: "Edit Employee Number Series",
    serviceProvider: "Service Provider",

    // Overtime Configuration
    configuration: "Configuration",
    overtimeConfig: "Overtime Configuration",
    overtimeCoverage: "Coverage",
    activeOvertimeConfigurations:
      "There are currently active overtime configurations. To modify the coverage, you must first inactivate these configurations.",
    salaryType: "Salary Type",
    specialWorkDays: "Overtime On",
    wageIndex: "Wage Index",
    overtimeType: "Overtime Type",
    amount: "Amount",
    viewOvertimeConfig: "View Configuration",
    viewAll: "View All",
    employees: "Employees",
    errorEmployeeList:
      "Something went wrong while fetching the employees list. Please try again.",
    noEmployeesFound:
      "It seems like there are no employees associated with the selected custom group. Please add some employees under the selected group or try choosing an another group.",
    customGroupEmployees: "Custom Group Employee(s)",
    addOvertimeConfig: "Add Configuration",
    editOvertimeConfig: "Edit Configuration",
    addCustomGroup: "Add Custom Group",
    updateOvertimeCoverage: "Are you sure to update coverage?",
    overtimeConfigSetTo:
      "Overtime configuration is set to {current}, conflicting with the platform's {conflicting} level setting. Please adjust the platform to {adjust} or establish a new configuration.",
    coverageType: "Coverage Type",
    overTimeEmptyText1:
      "Overtime Configuration for Admin allows centralized control over how employee overtime is calculated, approved, and paid. The admin can define eligibility criteria based on organization or custom group and choose the calculation method—whether flat rate, no slab or slab-based. The system supports customization of overtime rates for regular days, rest days, and holidays, aligning with company policy or statutory requirements.",
    overTimeEmptyText2:
      "The admin can configure different pay percentages for regular working days, rest days, special non-working holidays, and regular holidays. This ensures that employees are fairly compensated according to applicable labor laws. Overall, the Overtime Configuration offers a flexible, policy-driven, and audit-compliant framework for managing employee overtime efficiently across the organization.",
    overtimeAddSuccess: "Overtime configuration added successfully",
    overtimeUpdateSuccess: "Overtime configuration updated successfully",
    coverageUpdateSuccess: "Overtime coverage updated successfully.",
    noAccessMessage: "You don't have access to perform this action.",

    // General Settings - Nested structure
    general: {
      // General
      general: "General",
      generalSettings: "General Settings",

      // Brand Assets
      brandAssets: "Brand Assets",
      companyLogo: "Company Logo",
      companyLogoHint: "Upload company logo (PNG, JPEG, JPG). Max size: 2MB",
      favicon: "Favicon",
      faviconHint: "Upload favicon (PNG, JPEG, JPG, ICO). Max size: 1MB",
      faviconPreview: "Favicon Preview",
      useCompanyLogoAsProductLogo: "Use Company Logo as Product Logo",
      logo: "Logo",

      // Layout and Styling
      layoutAndStyling: "Layout and Styling",
      colorScheme: "Color Scheme",
      primary: "Primary",
      secondary: "Secondary",
      hover: "Hover",
      colorControls: "Color Controls",
      primaryColor: "Primary Color",
      secondaryColor: "Secondary Color",
      hoverColor: "Hover Color",
      dataTableHeaderPanel: "Data Table Header Panel",
      dataTableHeaderText: "Data Table Header Text",

      // Color Picker
      chooseColor: "Choose {colorType} Color",
      presetColors: "Preset Colors",

      // Validation Messages
      fileSizeMustBeLessThan2MB: "File size must be less than 2MB",
      fileSizeMustBeLessThan1MB: "File size must be less than 1MB",

      // Success Messages
      updatedSuccessfully: "General settings updated successfully!",

      // Tab Names
      customFields: "Custom Fields",
      emailTemplates: "Email Templates",

      // Tooltips and Help Text
      appliesAcrossApplication: "Applies across the application",
      noChangesToUpdate: "There are no changes to be updated",
    },

    // Career Page Designer - Nested structure
    careerPage: {
      // Main component
      livePreview: "Live Preview",
      viewOpenings: "View Openings",
      configurationSavedSuccessfully: "Configuration saved successfully!",
      failedToUploadAssets: "Failed to upload assets",

      // Brand Assets (reusing from general section where possible)
      careerLogo: "Career Page Logo",
      careerLogoHint:
        "Upload Career Page Logo (PNG, JPEG, JPG). Max size: 2MB.",
      bannerImage: "Banner Image",
      filesSelected: "{count} files selected",
      bannerImageHint: "Recommended size: 1920×600px. Supports PNG, JPEG, JPG.",
      logoAlt: "Logo",
      careerLogoTooltip:
        "Overrides the company logo and presents a unique logo for the career page in alignment to your company's public website and brand.",

      // AI Suggestions
      aiPoweredBannerSuggestions: "AI-Powered Banner Suggestions",
      howItWorks: "How it works",
      aiDescription:
        "Describe your brand and industry. Our AI will suggest banner images tailored to your company's identity.",
      companyBranding: "Company Branding",
      companyBrandingPlaceholder:
        "e.g., We are a minimalist tech startup with a blue and white color scheme. Our logo is a simple geometric shape...",
      industryTrends: "Industry Trends",
      industryTrendsPlaceholder:
        "e.g., Current trends in our industry favor abstract, data-visualization style imagery and diverse team photos...",
      getSuggestions: "Get Suggestions",
      aiGeneratedBannerSuggestions: "AI-Generated Banner Suggestions",
      aiSuggestionsDescription:
        "Choose from our AI-generated banner suggestions tailored to your company's identity.",
      backToForm: "Back to Form",
      applySelected: "Apply Selected",

      // Content Customization
      contentCustomization: "Content Customization",
      bannerOverlayHeading: "Banner Overlay Heading",
      bannerOverlaySubText: "Banner Overlay Sub Text",
      enterBannerHeading: "Enter banner heading...",
      enterBannerSubText: "Enter banner sub text...",
      bannerHeading: "Banner Heading",
      bannerSubText: "Banner Sub Text",
      bannerHeadingDescription:
        "Main heading text that will appear overlaid on your banner image.",
      bannerSubTextDescription:
        "Supporting text that will appear below the heading on your banner image.",

      // Text Positioning
      textPosition: "Text Position",
      horizontalPosition: "Horizontal Position",
      verticalPosition: "Vertical Position",
      left: "Left",
      center: "Center",
      right: "Right",
      top: "Top",
      middle: "Middle",
      bottom: "Bottom",
      positionPreview: "Position Preview",

      // Typography Controls
      typographyControls: "Typography Controls",
      bannerFontFamily: "Banner Font Family",
      bannerHeadingFontSize: "Banner Heading Font Size",
      bannerSubtextFontSize: "Banner Subtext Font Size",
      bannerFontColor: "Banner Font Color",
      chooseBannerFontColor: "Choose Banner Font Color",

      // Layout and Styling - Banner Overlay
      bannerOverlay: "Banner Overlay",
      bannerOverlayOpacity: "Banner Overlay Opacity",

      // Color Palette Preview
      colorPalettePreview: "Color Palette Preview",
      primaryButtons: "Primary (Buttons)",
      secondaryPreview: "Secondary (Preview)",
      hoverHighlighting: "Hover (Highlighting)",
      dataTableHeaderPanel: "Data Table Header Panel",
      dataTableHeaderText: "Data Table Header Text",

      // Career Preview
      companyLogoAlt: "Company Logo",
      logoPlaceholder: "200×80",
      openPositions: "Open Positions (sample data)",
      apply: "Apply",

      // Validation and Error Messages
      fileSizeMustBeLessThan2MB: "File size must be less than 2MB",
      fileSizeMustBeLessThan1MB: "File size must be less than 1MB",
      fileSizeMustBeLessThan3MB: "File size must be less than 3MB",
      fileSizeMustBeLessThanMB: "File size must be less than {size}MB",
      failedToProcessFile: "Failed to process {type}. Please try again.",
      failedToGenerateSuggestions:
        "Failed to generate suggestions. Please try again.",
    },
    /* ----- Recruitment Settings ----- */
    failedToLoadRecruitmentSettings:
      "Failed to load Recruitment Settings. Please try again.",
    failedToSaveSettings: "Failed to save settings. Please try again.",

    // Section Headers
    generalSettings: "General Settings",
    integrationSettings: "Integration Settings",
    candidateExperiencePortalConfiguration:
      "Candidate Experience Portal Configuration",

    // Field Labels
    jobPostVisibility: "Job Post Visibility",
    centralisedRecruitment: "Centralised Recruitment",
    allowModifyInterviewRounds:
      "Allow to modify the interview rounds after the job post creation",
    equalOpportunityStatement:
      "Equal opportunity statement to present in the candidate application form",
    enableNewFeatures: "Enable Recently Introduced Features",
    enableMppIntegration: "Enable MPP Integration",
    allowEditingJobTitles: "Allow editing Job Titles",
    automaticallySendSignedDocument: "Automatically send the signed document",
    tenantUniqueKey: "Tenant Unique Key",
    allowCandidatePortalAccessNewApplicant:
      "Allow Candidate Portal Access for New Applicant",
    allowBlacklistedCandidatePortalAccess:
      "Allow Blacklisted Candidate Portal Access",
    allowArchivedCandidatePortalAccess:
      "Allow Archived Candidate Portal Access",
    otpExpiryDuration: "OTP Expiry Duration (in minutes)",
    candidateApplicationFormType: "Candidate Application Form Type",
    allowCandidateMultipleJobs: "Allow candidate to apply for multiple jobs",
    careerPortalFilters: "Career Portal Filters",

    // Tooltips
    jobPostVisibilityTooltip:
      "Defines the visibility of job posts based on user group or organisation unit.",
    jobPostVisibilityTooltipNoFieldForce:
      "Defines the visibility of job posts based on user group or the entire organisation.",
    centralisedRecruitmentTooltip:
      "Allows recruiters to access and manage job posts created across the entire organisation.",
    allowModifyInterviewRoundsTooltip:
      "Allows recruiters to modify interview rounds after the job posting.",
    equalOpportunityStatementTooltip:
      "Customize the message displayed to candidates about your organization's commitment to diversity, inclusion, and equal employment opportunities.",
    enableNewFeaturesTooltip:
      "Enables recently introduced features for user experience before activating them permanently.",
    enableMppIntegrationTooltip:
      "Allows job postings to be created based on approved job requisitions from the Manpower Planning (MPP) module.",
    allowEditingJobTitlesTooltip:
      "When enabled, this allows recruiters to modify the job title while creating a job post against a job requisition request.",
    automaticallySendSignedDocumentTooltip:
      "If enabled, the system sends the signed document as an attachment in the email notification after receiving the signature of all parties.",
    tenantUniqueKeyTooltip:
      "Unique identifier used to synchronize job postings with external systems.",
    allowCandidatePortalAccessNewApplicantTooltip:
      "Grants candidates access to the portal to view their profile, manage details, and track application progress.",
    allowBlacklistedCandidatePortalAccessTooltip:
      "Permits blacklisted candidates to access portal and view rejection reasons",
    allowArchivedCandidatePortalAccessTooltip:
      "Allows candidates from completed hiring processes to access their historical data",
    otpExpiryDurationTooltip:
      "Duration for which OTP remains valid for candidate portal login",
    candidateApplicationFormTypeTooltip:
      "Simple form collects basic info; Detailed form includes comprehensive candidate profiling",
    allowCandidateMultipleJobsTooltip:
      "Allows candidates to apply for multiple positions simultaneously with a single click.",
    careerPortalFiltersTooltip:
      "Filters available to candidates when browsing job openings",

    // Placeholders and Messages
    enterTenantUniqueKey: "Enter tenant unique key",
    thereAreNoChangesToBeUpdated: "There are no changes to be updated",
    areYouSureToExitThisForm: "Are you sure to exit this form?",
  },
  fr: {
    /* ----- Settings ----- */
    // Module names
    settings: "Paramètres",

    // Core HR
    timeOff: "Congés",
    compOff: "Repos compensatoire",
    specialWages: "Rémunérations spéciales",
    overtime: "Heures supplémentaires",
    attendanceConfiguration: "Configuration de la présence",
    geoFencing: "Géorepérage et pointage par selfie",
    holidays: "Jours fériés",
    preApprovals: "Pré-approbations",
    lopRecovery: "Récupération des jours non payés",
    roster: "Planning",
    roles: "Rôles",

    // Data Loss Prevention Settings
    dataLossPrevention: "Prévention de la perte de données",
    internetAccessControl: "Contrôle d'accès Internet",
    locationTracking: "Suivi de localisation",
    keyLogging: "Enregistrement des frappes",
    additionalScreenshots: "Captures d'écran supplémentaires",
    auditLog: "Journal des audits",

    // Productivity Monitoring
    members: "Membres",
    reports: "Rapports",
    productivityMonitoring: "Suivi de la productivité",

    // Common
    status: "Statut",
    enabled: "Activé",
    disabled: "Désactivé",
    enable: "Activer",
    disable: "Désactiver",
    active: "Actif",
    inactive: "Inactif",
    new: "Nouveau",
    added: "Ajouté",
    updated: "Mis à jour",
    exitFormWarning: "Êtes-vous sûr de vouloir quitter ce formulaire ?",

    noChangesToUpdate: "Il n'y a aucune modification à mettre à jour",
    confirmExitForm: "Êtes-vous sûr de vouloir quitter ce formulaire ?",
    noRecordsFound:
      "Il n'y a pas de {formName} pour les filtres/recherches sélectionnés.",
    ipAddress: "Adresse IP",
    cidr: "CIDR",
    employee: "Employé",
    employeeName: "Nom de l'employé",
    employeeId: "ID de l'employé",
    organization: "Organisation",
    organizationLevel: "Niveau de l'organisation",
    employeeLevel: "Niveau de l'employé",
    addedOn: "Ajouté le",
    updatedOn: "Mis à jour le",
    addedBy: "Ajouté par",
    updatedBy: "Mis à jour par",
    actions: "Actions",
    yes: "Oui",
    no: "Non",
    save: "Enregistrer",
    cancel: "Annuler",
    close: "Fermer",
    edit: "Modifier",
    delete: "Supprimer",
    add: "Ajouter",
    addNew: "Ajouter nouveau",
    search: "Rechercher",
    filter: "Filtrer",
    resetFilter: "Réinitialiser le filtre",
    resetFilterSearch: "Réinitialiser le filtre/recherche",
    export: "Exporter",
    refresh: "Actualiser",
    setAll: "Définir tout",
    retry: "Réessayer",
    changedSettings: "Vous avez modifié {count} paramètre(s)",
    addConfiguration: "Ajouter une configuration",
    itemPerPage: "Éléments par page :",
    pageText: "{start}-{end} sur {total}",
    apply: "Appliquer",
    reset: "Réinitialiser",
    designation: "Fonction",
    department: "Département",

    // Internet Access Control
    enableInternetAccessControl: "Activer le contrôle d'accès Internet",
    notifyInternetAccessViolation: "Notifier les violations d'accès Internet",
    internetAccessUpdateFrequency: "Fréquence de mise à jour d'accès Internet",
    overrideInternetAccess: "Remplacer l'accès Internet",
    blockedDomains: "Domaines bloqués",
    whitelistedDomains: "Domaines autorisés",
    domainName: "Nom de domaine",
    category: "Catégorie",
    productive: "Productif",
    unproductive: "Improductif",
    neutral: "Neutre",
    addBlockedDomain: "Ajouter un domaine bloqué",
    addWhitelistedDomain: "Ajouter un domaine autorisé",
    domainCategory: "Catégorie de domaine",
    tooltipDomainContent:
      "L'administrateur peut remplacer le contrôle d'accès Internet au niveau de l'organisation par un contrôle d'accès au niveau de l'employé en modifiant la configuration ci-dessous",
    notifyButtonContent:
      "Lorsque le contrôle d'accès Internet est défini sur 'Non', veuillez noter que la notification d'accès Internet sera définie sur 'Non' et ne pourra pas être modifiée.",
    accessDeniedUpdateMessage:
      "L'employé connecté n'a pas d'accès de mise à jour",
    saveOrCancelChanges:
      "Veuillez enregistrer ou annuler les modifications avant de continuer",
    enableInternetAccessMessage:
      "Veuillez activer la fonction de contrôle d'accès Internet pour permettre l'ajout ou la suppression de sites Web de la liste bloquée.",

    // Internet Access Control Modal
    websitesBlockedUnblocked: "Sites web (Bloqués/Débloqués)",
    appsBlockedUnblocked: "Applications (Bloquées/Débloquées)",
    customGroup: "Groupe personnalisé",
    selectCustomGroup: "Sélectionner un groupe personnalisé",
    copyClassificationFrom: "Copier la classification de",
    copyNow: "Copier maintenant",
    wildcardConfiguration: "Configuration des caractères génériques",
    blockAccess: "Bloquer l'accès",
    back: "Retour",
    followingWebsitesBlocked:
      "Les sites web suivants seront bloqués pour l'accès.",
    noSuchDomain: "Ce domaine n'existe pas, ou est déjà bloqué.",
    thereAreNoBlockedWebsites: "Il n'y a pas de sites web bloqués",
    addMore: "Ajouter plus",
    changesHaveBeenDone: "{count} modification(s) ont été effectuées",
    domainRemovedSuccess:
      "Domaine supprimé de la liste de blocage avec succès.",
    domainStatusUpdatedSuccess: "Statut du domaine mis à jour avec succès",
    domainAddedSuccess: "Domaine ajouté avec succès à la liste de blocage.",
    clearAll: "Tout effacer",
    all: "Tous",
    blocked: "Bloqué",
    unblocked: "Débloqué",
    block: "Bloquer",
    unblock: "Débloquer",
    domainStatusCannotBeChanged:
      "Le statut du domaine ne peut pas être modifié car il a été défini par la configuration des caractères génériques",
    noItemsToCategorizePre: "Vous n'avez pas de ",
    noItemsToCategorizePost: " à catégoriser.",
    moreDetails: "Plus de détails",

    // Location Tracking
    locationTrackingSettings: "Paramètres de suivi de localisation",
    enableLocationTracking: "Activer le suivi de localisation",
    workLocation: "Lieu de travail",
    ipRange: "Plage d'IP",
    addWorkLocation: "Ajouter un lieu de travail",
    editWorkLocation: "Modifier un lieu de travail",
    locationName: "Nom de l'emplacement",
    locationTrackingSettingsUpdatedSuccess:
      "Paramètres de suivi de localisation mis à jour avec succès",
    workLocationAddedSuccess: "Lieu de travail ajouté avec succès",
    workLocationUpdatedSuccess: "Lieu de travail mis à jour avec succès",
    locationTrackingDescription:
      "Suivre les emplacements des employés en fonction des adresses IP",
    ipRangeFormat: "Format de plage IP (ex: ***********-*************)",
    locationTrackingLongDescription:
      "Pour assurer une catégorisation précise de l'activité des utilisateurs dans le tableau de bord de localisation, veuillez attribuer une plage spécifique d'adresses IP à votre réseau de bureau.",
    ipAddressTypePublic: "Type d'adresse IP: Publique (IP Internet)",
    addressFormatIPv4: "Format d'adresse: IPv4 uniquement",
    ipRangeSpecify:
      "Plage: Spécifiez l'adresse IP ou la plage (y compris le masque de sous-réseau)",
    ipAddressImportantNote:
      "Important: Les adresses IP attribuées aux activités passées ne seront pas mises à jour rétrospectivement.",
    locationTrackingNote1:
      "Les paramètres de suivi de localisation peuvent aider à superviser et à gérer l'utilisation du réseau en enregistrant les adresses IP des appareils connectés au réseau de votre entreprise",
    locationTrackingNote2:
      "Les paramètres incluent généralement des options pour activer ou désactiver le suivi, ajouter de nouveaux lieux de travail et adresses IP",

    // Key Logging
    keyLoggingSettings: "Paramètres d'enregistrement des frappes",
    captureControlKeys: "Capturer les touches de contrôle",
    keyStrokeCount: "Nombre de frappes",
    applicationName: "Nom de l'application",
    websites: "Sites web",
    windowTitle: "Titre de la fenêtre",

    // Additional Screenshots
    additionalScreenshotsSettings:
      "Paramètres de captures d'écran supplémentaires",
    captureScreenshot: "Capturer des captures d'écran",
    screenshotSchedule: "Planification des captures d'écran",
    fixed: "Fixe",
    random: "Aléatoire",
    screenshotFrequency: "Fréquence des captures d'écran",
    screenshotFrequencyType: "Type de fréquence des captures d'écran",
    screenshotsPerFrequency: "Captures d'écran par fréquence",
    additionalScreenshotsPerFrequency:
      "Captures d'écran supplémentaires par fréquence",
    screenshotBlur: "Flou des captures d'écran",
    minutes: "Minutes",
    hours: "Heures",

    // Messages
    confirmationHeading: "Êtes-vous sûr de vouloir changer le statut ?",
    settingsUpdatedSuccess: "Paramètres mis à jour avec succès",
    locationTrackingUpdatedSuccess:
      "Paramètres de suivi de localisation mis à jour avec succès",

    keyLoggingUpdatedSuccess:
      "Paramètres d'enregistrement des frappes mis à jour avec succès",
    screenshotsUpdatedSuccess:
      "Paramètres de captures d'écran mis à jour avec succès",
    accessDeniedMessage: "Vous n'avez pas accès pour effectuer cette action",

    // Help text
    internetAccessControlHelp:
      "Le contrôle d'accès Internet aide à surveiller et à contrôler l'accès aux sites Web et aux domaines dans toute votre organisation.",
    locationTrackingHelp:
      "Le suivi de localisation aide à identifier si les employés travaillent depuis le bureau ou à distance en fonction des adresses IP.",
    keyLoggingHelp:
      "L'enregistrement des frappes suit l'activité du clavier pour surveiller la productivité et détecter les fuites potentielles de données.",
    keyLoggingDescription:
      "L'activation de l'enregistrement des frappes capturera toutes les frappes effectuées par les utilisateurs lorsqu'ils interagissent avec l'application, aidant à surveiller l'activité des utilisateurs. Ces données peuvent fournir des informations sur les modèles d'utilisation et assurer la conformité aux politiques de sécurité.",
    additionalScreenshotsHelp:
      "Les captures d'écran supplémentaires permettent de capturer des captures d'écran supplémentaires pour surveiller l'activité des employés au-delà des intervalles réguliers.",
    additionalScreenshotsNote1:
      "Des captures d'écran périodiques ou déclenchées sont capturées pour donner un contexte visuel aux journaux, aidant à vérifier les tâches réellement effectuées.",
    additionalScreenshotsNote2:
      "Prend des captures d'écran automatiques à intervalles réguliers ou en fonction de déclencheurs spécifiques (par exemple, l'ouverture d'applications ou de sites Web spécifiques).",

    // Employee Number Series
    employeeNumberSeries: "Série de numéros d'employé",
    employeeNumberSeriesSettings: "Paramètres de série de numéros d'employé",
    enableEmployeeNumberSeries: "Activer la série de numéros d'employé",
    prefix: "Préfixe",
    suffix: "Suffixe",
    noOfDigits: "Nombre de chiffres",
    nextNumber: "Prochain numéro",
    viewEmployeeNumberSeries: "Voir la série de numéros d'employé",
    addEmployeeNumberSeries: "Ajouter une série de numéros d'employé",
    editEmployeeNumberSeries: "Modifier la série de numéros d'employé",
    serviceProvider: "Fournisseur de services",

    // Overtime Configuration
    configuration: "Configuration",
    overtimeConfig: "Configuration des heures supplémentaires",
    overtimeCoverage: "Couverture",
    activeOvertimeConfigurations:
      "Il existe actuellement des configurations d'heures supplémentaires actives. Pour modifier la couverture, vous devez d'abord désactiver ces configurations.",
    salaryType: "Type de salaire",
    specialWorkDays: "Heures supplémentaires le",
    wageIndex: "Index des salaires",
    overtimeType: "Type d'heures supplémentaires",
    amount: "Montant",
    viewOvertimeConfig: "Afficher la configuration",
    viewAll: "Afficher tout",
    employees: "Employés",
    errorEmployeeList:
      "Quelque chose s'est mal passé lors de la récupération de la liste des employés. Veuillez réessayer.",
    noEmployeesFound:
      "Il semble que aucun employé n'est associé au groupe personnalisé sélectionné. Veuillez ajouter des employés sous le groupe sélectionné ou essayer de choisir un autre groupe.",
    customGroupEmployees: "Employé(s) du groupe personnalisé",
    addOvertimeConfig: "Ajouter une configuration",
    editOvertimeConfig: "Modifier la configuration",
    addCustomGroup: "Ajouter un groupe personnalisé",
    updateOvertimeCoverage:
      "Êtes-vous sûr de vouloir mettre à jour la couverture?",
    overtimeConfigSetTo:
      "La configuration des heures supplémentaires est définie sur {current}, ce qui est en conflit avec le paramètre de niveau {conflicting} de la plateforme. Veuillez ajuster la plateforme à {adjust} ou établir une nouvelle configuration.",
    coverageType: "Type de couverture",
    overTimeEmptyText1:
      "La configuration des heures supplémentaires pour l’administrateur permet un contrôle centralisé de la manière dont les heures supplémentaires des employés sont calculées, approuvées et payées. L’administrateur peut définir des critères d’éligibilité en fonction de l’organisation ou d’un groupe personnalisé, et choisir la méthode de calcul — taux fixe, sans palier ou par paliers. Le système permet de personnaliser les taux d’heures supplémentaires pour les jours normaux, les jours de repos et les jours fériés, en conformité avec la politique de l’entreprise ou les exigences légales.",
    overTimeEmptyText2:
      "L’administrateur peut configurer différents pourcentages de rémunération pour les jours ouvrables ordinaires, les jours de repos, les jours fériés spéciaux non travaillés et les jours fériés réguliers. Cela garantit une rémunération équitable des employés, conformément aux lois du travail applicables. Dans l’ensemble, la configuration des heures supplémentaires offre un cadre flexible, fondé sur les politiques et conforme aux audits pour gérer efficacement les heures supplémentaires à l’échelle de l’organisation.",
    overtimeAddSuccess:
      "Configuration des heures supplémentaires ajoutée avec succès",
    overtimeUpdateSuccess:
      "Configuration des heures supplémentaires mise à jour avec succès",
    coverageUpdateSuccess:
      "La couverture des heures supplémentaires a été mise à jour avec succès.",
    noAccessMessage: "Vous n'avez pas l'autorisation d'effectuer cette action.",

    // General Settings - Nested structure
    general: {
      // General
      general: "Général",
      generalSettings: "Paramètres généraux",

      // Brand Assets
      brandAssets: "Éléments de marque",
      companyLogo: "Logo de l'entreprise",
      companyLogoHint:
        "Télécharger le logo de l'entreprise (PNG, JPEG, JPG). Taille max : 2 Mo",
      favicon: "Favicon",
      faviconHint:
        "Télécharger le favicon (PNG, JPEG, JPG, ICO). Taille max : 1 Mo",
      faviconPreview: "Aperçu du favicon",
      useCompanyLogoAsProductLogo:
        "Utiliser le logo de l'entreprise comme logo du produit",
      logo: "Logo",

      // Layout and Styling
      layoutAndStyling: "Mise en page et style",
      colorScheme: "Schéma de couleurs",
      primary: "Primaire",
      secondary: "Secondaire",
      hover: "Survol",
      colorControls: "Contrôles de couleur",
      primaryColor: "Couleur primaire",
      secondaryColor: "Couleur secondaire",
      hoverColor: "Couleur de survol",
      dataTableHeaderPanel: "Panneau d'en-tête du tableau de données",
      dataTableHeaderText: "Texte d'en-tête du tableau de données",

      // Color Picker
      chooseColor: "Choisir la couleur {colorType}",
      presetColors: "Couleurs prédéfinies",

      // Validation Messages
      fileSizeMustBeLessThan2MB:
        "La taille du fichier doit être inférieure à 2 Mo",
      fileSizeMustBeLessThan1MB:
        "La taille du fichier doit être inférieure à 1 Mo",

      // Success Messages
      updatedSuccessfully: "Paramètres généraux mis à jour avec succès !",

      // Tab Names
      customFields: "Champs personnalisés",
      emailTemplates: "Modèles d'e-mail",

      // Tooltips and Help Text
      appliesAcrossApplication: "S'applique à toute l'application",
      noChangesToUpdate: "Il n'y a pas de changements à mettre à jour",
    },

    // Career Page Designer - Nested structure
    careerPage: {
      // Main component
      livePreview: "Aperçu en direct",
      viewOpenings: "Voir les offres",

      configurationSavedSuccessfully: "Configuration sauvegardée avec succès!",
      failedToUploadAssets: "Échec du téléchargement des ressources",

      // Brand Assets (reusing from general section where possible)
      careerLogo: "Logo de la page carrière",
      careerLogoHint:
        "Télécharger le logo de la page carrière (PNG, JPEG, JPG). Taille max : 2 Mo",
      bannerImage: "Image de bannière",
      filesSelected: "{count} fichiers sélectionnés",
      bannerImageHint:
        "Taille recommandée : 1920×600px. Supporte PNG, JPEG, JPG.",
      logoAlt: "Logo",
      careerLogoTooltip:
        "Remplace le logo de l'entreprise et présente un logo unique pour la page carrière en adéquation avec le site web public de votre entreprise et votre marque.",

      // AI Suggestions
      aiPoweredBannerSuggestions: "Suggestions de bannière alimentées par l'IA",
      howItWorks: "Comment ça marche",
      aiDescription:
        "Décrivez votre marque et votre secteur. Notre IA suggérera des images de bannière adaptées à l'identité de votre entreprise.",
      companyBranding: "Image de marque de l'entreprise",
      companyBrandingPlaceholder:
        "ex., Nous sommes une startup technologique minimaliste avec un schéma de couleurs bleu et blanc. Notre logo est une forme géométrique simple...",
      industryTrends: "Tendances de l'industrie",
      industryTrendsPlaceholder:
        "ex., Les tendances actuelles de notre industrie favorisent l'imagerie abstraite de style visualisation de données et les photos d'équipes diversifiées...",
      getSuggestions: "Obtenir des suggestions",
      aiGeneratedBannerSuggestions: "Suggestions de bannière générées par l'IA",
      aiSuggestionsDescription:
        "Choisissez parmi nos suggestions de bannière générées par l'IA adaptées à l'identité de votre entreprise.",
      backToForm: "Retour au formulaire",
      applySelected: "Appliquer la sélection",

      // Content Customization
      contentCustomization: "Personnalisation du contenu",
      bannerOverlayHeading: "Titre de superposition de bannière",
      bannerOverlaySubText: "Sous-texte de superposition de bannière",
      enterBannerHeading: "Entrez le titre de la bannière...",
      enterBannerSubText: "Entrez le sous-texte de la bannière...",
      bannerHeading: "Titre de bannière",
      bannerSubText: "Sous-texte de bannière",
      bannerHeadingDescription:
        "Texte de titre principal qui apparaîtra en superposition sur votre image de bannière.",
      bannerSubTextDescription:
        "Texte de support qui apparaîtra sous le titre sur votre image de bannière.",

      // Text Positioning
      textPosition: "Position du texte",
      horizontalPosition: "Position horizontale",
      verticalPosition: "Position verticale",
      left: "Gauche",
      center: "Centre",
      right: "Droite",
      top: "Haut",
      middle: "Milieu",
      bottom: "Bas",
      positionPreview: "Aperçu de la position",

      // Typography Controls
      typographyControls: "Contrôles de typographie",
      bannerFontFamily: "Famille de police de bannière",
      bannerHeadingFontSize: "Taille de police du titre de bannière",
      bannerSubtextFontSize: "Taille de police du sous-texte de bannière",
      bannerFontColor: "Couleur de police de bannière",
      chooseBannerFontColor: "Choisir la couleur de police de bannière",

      // Layout and Styling - Banner Overlay
      bannerOverlay: "Superposition de bannière",
      bannerOverlayOpacity: "Opacité de superposition de bannière",

      // Color Palette Preview
      colorPalettePreview: "Aperçu de la palette de couleurs",
      primaryButtons: "Primaire (Boutons)",
      secondaryPreview: "Secondaire (Aperçu)",
      hoverHighlighting: "Survol (Mise en évidence)",
      dataTableHeaderPanel: "Panneau d'en-tête de tableau de données",
      dataTableHeaderText: "Texte d'en-tête de tableau de données",

      // Career Preview
      companyLogoAlt: "Logo de l'entreprise",
      logoPlaceholder: "200×80",
      openPositions: "Postes ouverts (données d'exemple)",
      apply: "Postuler",

      // Validation and Error Messages
      fileSizeMustBeLessThan2MB:
        "La taille du fichier doit être inférieure à 2 Mo",
      fileSizeMustBeLessThan1MB:
        "La taille du fichier doit être inférieure à 1 Mo",
      fileSizeMustBeLessThan3MB:
        "La taille du fichier doit être inférieure à 3 Mo",
      fileSizeMustBeLessThanMB:
        "La taille du fichier doit être inférieure à {size} Mo",
      failedToProcessFile: "Échec du traitement de {type}. Veuillez réessayer.",
      failedToGenerateSuggestions:
        "Échec de la génération de suggestions. Veuillez réessayer.",
    },
    failedToLoadRecruitmentSettings:
      "Échec du chargement des paramètres de recrutement. Veuillez réessayer.",
    failedToSaveSettings:
      "Échec de la sauvegarde des paramètres. Veuillez réessayer.",

    /* ----- Recruitment Settings ----- */
    // Section Headers
    generalSettings: "Paramètres généraux",
    integrationSettings: "Paramètres d'intégration",
    candidateExperiencePortalConfiguration:
      "Configuration du portail d'expérience candidat",

    // Field Labels
    jobPostVisibility: "Visibilité des offres d'emploi",
    centralisedRecruitment: "Recrutement centralisé",
    allowModifyInterviewRounds:
      "Permettre de modifier les tours d'entretien après la création de l'offre d'emploi",
    equalOpportunityStatement:
      "Déclaration d'égalité des chances à présenter dans le formulaire de candidature",
    enableNewFeatures: "Activer les fonctionnalités récemment introduites",
    enableMppIntegration: "Activer l'intégration MPP",
    allowEditingJobTitles: "Permettre la modification des titres de poste",
    automaticallySendSignedDocument:
      "Envoyer automatiquement le document signé",
    tenantUniqueKey: "Clé unique du locataire",
    allowCandidatePortalAccessNewApplicant:
      "Autoriser l'accès au portail candidat pour les nouveaux candidats",
    allowBlacklistedCandidatePortalAccess:
      "Autoriser l'accès au portail pour les candidats sur liste noire",
    allowArchivedCandidatePortalAccess:
      "Autoriser l'accès au portail pour les candidats archivés",
    otpExpiryDuration: "Durée d'expiration OTP (en minutes)",
    candidateApplicationFormType: "Type de formulaire de candidature",
    allowCandidateMultipleJobs:
      "Permettre au candidat de postuler à plusieurs emplois",
    careerPortalFilters: "Filtres du portail carrière",

    // Tooltips
    jobPostVisibilityTooltip:
      "Définit la visibilité des offres d'emploi basée sur le groupe d'utilisateurs ou l'unité organisationnelle.",
    jobPostVisibilityTooltipNoFieldForce:
      "Définit la visibilité des offres d'emploi basée sur le groupe d'utilisateurs ou l'ensemble de l'organisation.",
    centralisedRecruitmentTooltip:
      "Permet aux recruteurs d'accéder et de gérer les offres d'emploi créées dans toute l'organisation.",
    allowModifyInterviewRoundsTooltip:
      "Permet aux recruteurs de modifier les tours d'entretien après la publication de l'emploi.",
    equalOpportunityStatementTooltip:
      "Personnalisez le message affiché aux candidats concernant l'engagement de votre organisation envers la diversité, l'inclusion et l'égalité des chances d'emploi.",
    enableNewFeaturesTooltip:
      "Active les fonctionnalités récemment introduites pour l'expérience utilisateur avant de les activer définitivement.",
    enableMppIntegrationTooltip:
      "Permet de créer des offres d'emploi basées sur des demandes d'emploi approuvées du module de planification des effectifs (MPP).",
    allowEditingJobTitlesTooltip:
      "Lorsqu'il est activé, cela permet aux recruteurs de modifier le titre du poste lors de la création d'une offre d'emploi à la demande d'une demande d'emploi.",
    automaticallySendSignedDocumentTooltip:
      "Si activé, le système envoie le document signé en tant que pièce jointe dans la notification par e-mail après avoir reçu la signature de toutes les parties.",
    tenantUniqueKeyTooltip:
      "Identifiant unique utilisé pour synchroniser les offres d'emploi avec les systèmes externes.",
    allowCandidatePortalAccessNewApplicantTooltip:
      "Accorde aux candidats l'accès au portail pour consulter leur profil, gérer les détails et suivre les progrès de la candidature.",
    allowBlacklistedCandidatePortalAccessTooltip:
      "Permet aux candidats sur liste noire d'accéder au portail et de voir les raisons de rejet",
    allowArchivedCandidatePortalAccessTooltip:
      "Permet aux candidats des processus de recrutement terminés d'accéder à leurs données historiques",
    otpExpiryDurationTooltip:
      "Durée pendant laquelle l'OTP reste valide pour la connexion au portail candidat",
    candidateApplicationFormTypeTooltip:
      "Le formulaire simple collecte les informations de base ; le formulaire détaillé inclut un profilage complet du candidat",
    allowCandidateMultipleJobsTooltip:
      "Permet aux candidats de postuler à plusieurs postes simultanément en un seul clic.",
    careerPortalFiltersTooltip:
      "Filtres disponibles pour les candidats lors de la navigation dans les offres d'emploi",

    // Placeholders and Messages
    enterTenantUniqueKey: "Entrez la clé unique du locataire",
    thereAreNoChangesToBeUpdated: "Il n'y a aucun changement à mettre à jour",
    areYouSureToExitThisForm:
      "Êtes-vous sûr de vouloir quitter ce formulaire ?",
  },
  ja: {
    /* ----- Settings ----- */
    // Module names
    settings: "設定",

    // Core HR
    timeOff: "休暇",
    compOff: "代休",
    specialWages: "特別賃金",
    overtime: "残業",
    attendanceConfiguration: "出勤設定",
    geoFencing: "ジオフェンス・自撮り出勤",
    holidays: "祝日",
    preApprovals: "事前承認",
    lopRecovery: "無給休暇の回収",
    roster: "勤務表",
    roles: "役割",

    // Data Loss Prevention Settings
    dataLossPrevention: "データ損失防止",
    internetAccessControl: "インターネットアクセス制御",
    locationTracking: "位置追跡",
    keyLogging: "キーロギング",
    additionalScreenshots: "追加スクリーンショット",
    auditLog: "監査ログ",

    // Productivity Monitoring
    members: "メンバー",
    reports: "レポート",
    productivityMonitoring: "生産性監視",

    // Common
    status: "ステータス",
    enabled: "有効",
    disabled: "無効",
    enable: "有効にする",
    disable: "無効にする",
    active: "アクティブ",
    inactive: "非アクティブ",
    new: "新規",
    added: "追加済み",
    updated: "更新済み",
    exitFormWarning: "このフォームを終了してもよろしいですか？",

    noChangesToUpdate: "更新する変更はありません",
    confirmExitForm: "このフォームを終了してもよろしいですか？",
    noRecordsFound: "選択したフィルター/検索に一致する{formName}はありません。",
    ipAddress: "IPアドレス",
    cidr: "CIDR",
    employee: "従業員",
    employeeName: "従業員名",
    employeeId: "従業員ID",
    organization: "組織",
    organizationLevel: "組織レベル",
    employeeLevel: "従業員レベル",
    addedOn: "追加日",
    updatedOn: "更新日",
    addedBy: "追加者",
    updatedBy: "更新者",
    actions: "アクション",
    yes: "はい",
    no: "いいえ",
    save: "保存",
    cancel: "キャンセル",
    close: "閉じる",
    edit: "編集",
    delete: "削除",
    add: "追加",
    addNew: "新規追加",
    search: "検索",
    filter: "フィルター",
    resetFilter: "フィルターをリセット",
    resetFilterSearch: "フィルター/検索をリセット",
    export: "エクスポート",
    refresh: "更新",
    setAll: "すべて設定",
    retry: "再試行",
    changedSettings: "{count}個の設定を変更しました",
    addConfiguration: "構成を追加",
    itemPerPage: "1ページあたりの項目数：",
    pageText: "{start}～{end} / {total}",
    apply: "適用",
    reset: "リセット",
    designation: "役職",
    department: "部署",

    // Internet Access Control
    enableInternetAccessControl: "インターネットアクセス制御を有効にする",
    notifyInternetAccessViolation: "インターネットアクセス違反を通知する",
    internetAccessUpdateFrequency: "インターネットアクセス更新頻度",
    overrideInternetAccess: "インターネットアクセスを上書きする",
    blockedDomains: "ブロックされたドメイン",
    whitelistedDomains: "ホワイトリストドメイン",
    domainName: "ドメイン名",
    category: "カテゴリー",
    productive: "生産的",
    unproductive: "非生産的",
    neutral: "中立的",
    addBlockedDomain: "ブロックドメインを追加",
    addWhitelistedDomain: "ホワイトリストドメインを追加",
    domainCategory: "ドメインカテゴリー",
    tooltipDomainContent:
      "管理者は、以下の設定を編集することで、組織レベルのインターネットアクセス制御を従業員レベルのアクセス制御で上書きできます",
    notifyButtonContent:
      "インターネットアクセス制御が'いいえ'に設定されている場合、インターネットアクセス通知は'いいえ'に設定され、変更できないことに注意してください。",
    accessDeniedUpdateMessage:
      "ログインしている従業員は更新アクセス権限がありません",
    saveOrCancelChanges: "続行する前に変更を保存またはキャンセルしてください",
    enableInternetAccessMessage:
      "ブロックリストからウェブサイトを追加または削除できるように、インターネットアクセス制御機能を有効にしてください。",

    // Internet Access Control Modal
    websitesBlockedUnblocked: "ウェブサイト（ブロック済み/ブロック解除済み）",
    appsBlockedUnblocked: "アプリ（ブロック済み/ブロック解除済み）",
    customGroup: "カスタムグループ",
    selectCustomGroup: "カスタムグループを選択",
    copyClassificationFrom: "分類をコピー元",
    copyNow: "今すぐコピー",
    wildcardConfiguration: "ワイルドカード設定",
    blockAccess: "アクセスをブロック",
    back: "戻る",
    followingWebsitesBlocked:
      "以下のウェブサイトへのアクセスがブロックされます。",
    noSuchDomain:
      "そのようなドメインは存在しないか、すでにブロックされています。",
    thereAreNoBlockedWebsites: "ブロックされたウェブサイトはありません",
    addMore: "さらに追加",
    changesHaveBeenDone: "{count}個の変更が行われました",
    domainRemovedSuccess: "ドメインがブロックリストから正常に削除されました。",
    domainStatusUpdatedSuccess: "ドメインステータスが正常に更新されました",
    domainAddedSuccess: "ドメインがブロックリストに正常に追加されました。",
    clearAll: "すべてクリア",
    all: "すべて",
    blocked: "ブロック済み",
    unblocked: "ブロック解除",
    block: "ブロック",
    unblock: "ブロック解除する",
    domainStatusCannotBeChanged:
      "ワイルドカード設定で設定されたため、ドメインステータスを変更できません",
    noItemsToCategorizePre: "分類する",
    noItemsToCategorizePost: "がありません。",
    moreDetails: "詳細情報",

    // Location Tracking
    locationTrackingSettings: "位置追跡設定",
    enableLocationTracking: "位置追跡を有効にする",
    workLocation: "勤務地",
    ipRange: "IP範囲",
    addWorkLocation: "勤務地を追加",
    editWorkLocation: "勤務地を編集",
    locationName: "場所名",
    locationTrackingSettingsUpdatedSuccess:
      "位置追跡設定が正常に更新されました",
    workLocationAddedSuccess: "勤務地が正常に追加されました",
    workLocationUpdatedSuccess: "勤務地が正常に更新されました",
    locationTrackingDescription: "IPアドレスに基づいて従業員の位置を追跡する",
    ipRangeFormat: "IP範囲形式（例：***********-*************）",
    locationTrackingLongDescription:
      "位置ダッシュボードでのユーザーアクティビティの正確な分類を確保するために、オフィスネットワークに特定のIP範囲を割り当ててください。",
    ipAddressTypePublic: "IPアドレスタイプ: パブリック（インターネットIP）",
    addressFormatIPv4: "アドレス形式: IPv4のみ",
    ipRangeSpecify:
      "範囲: IPアドレスまたは範囲を指定（サブネットマスクを含む）",
    ipAddressImportantNote:
      "重要: 過去のアクティビティに割り当てられたIPアドレスは遡って更新されません。",
    locationTrackingNote1:
      "位置追跡設定は、会社のネットワークに接続されているデバイスのIPアドレスを記録することで、ネットワーク使用状況を監視および管理するのに役立ちます",
    locationTrackingNote2:
      "設定には通常、追跡の有効化または無効化、新しい勤務地とIPアドレスの追加などのオプションが含まれています",

    // Key Logging
    keyLoggingSettings: "キーロギング設定",
    captureControlKeys: "コントロールキーをキャプチャ",
    keyStrokeCount: "キーストローク数",
    applicationName: "アプリケーション名",
    websites: "ウェブサイト",
    windowTitle: "ウィンドウタイトル",

    // Additional Screenshots
    additionalScreenshotsSettings: "追加スクリーンショット設定",
    captureScreenshot: "スクリーンショットをキャプチャ",
    screenshotSchedule: "スクリーンショットスケジュール",
    fixed: "固定",
    random: "ランダム",
    screenshotFrequency: "スクリーンショット頻度",
    screenshotFrequencyType: "スクリーンショット頻度タイプ",
    screenshotsPerFrequency: "頻度あたりのスクリーンショット",
    additionalScreenshotsPerFrequency: "頻度あたりの追加スクリーンショット",
    screenshotBlur: "スクリーンショットのぼかし",
    minutes: "分",
    hours: "時間",

    // Messages
    confirmationHeading: "ステータスを変更してもよろしいですか？",
    settingsUpdatedSuccess: "設定が正常に更新されました",
    locationTrackingUpdatedSuccess: "位置追跡設定が正常に更新されました",

    keyLoggingUpdatedSuccess: "キーロギング設定が正常に更新されました",
    screenshotsUpdatedSuccess: "スクリーンショット設定が正常に更新されました",
    accessDeniedMessage: "このアクションを実行する権限がありません",

    // Help text
    internetAccessControlHelp:
      "インターネットアクセス制御は、組織全体でのウェブサイトやドメインへのアクセスを監視および制御するのに役立ちます。",
    locationTrackingHelp:
      "位置追跡は、IPアドレスに基づいて従業員がオフィスからリモートで作業しているかどうかを識別するのに役立ちます。",
    keyLoggingHelp:
      "キーロギングはキーボード活動を追跡して生産性を監視し、潜在的なデータ漏洩を検出します。",
    keyLoggingDescription:
      "キーロギングを有効にすると、ユーザーがアプリケーションを操作する際のすべてのキーストロークが記録され、ユーザーの活動を監視するのに役立ちます。このデータは、使用パターンに関する洞察を提供し、セキュリティポリシーへの準拠を確保することができます。",
    additionalScreenshotsHelp:
      "追加スクリーンショットは、通常の間隔を超えて従業員の活動を監視するための追加のスクリーンショットをキャプチャすることができます。",
    additionalScreenshotsNote1:
      "定期的またはトリガーされたスクリーンショットは、ログに視覚的なコンテキストを提供し、実際に実行されているタスクを確認するのに役立ちます。",
    additionalScreenshotsNote2:
      "一定の間隔で、または特定のトリガー（特定のアプリケーションやウェブサイトを開くなど）に基づいて自動的にスクリーンショットを撮ります。",

    // Employee Number Series
    employeeNumberSeries: "従業員番号シリーズ",
    employeeNumberSeriesSettings: "従業員番号シリーズ設定",
    enableEmployeeNumberSeries: "従業員番号シリーズを有効にする",
    prefix: "プレフィックス",
    suffix: "サフィックス",
    noOfDigits: "桁数",
    nextNumber: "次の番号",
    viewEmployeeNumberSeries: "従業員番号シリーズを表示",
    addEmployeeNumberSeries: "従業員番号シリーズを追加",
    editEmployeeNumberSeries: "従業員番号シリーズを編集",
    serviceProvider: "サービスプロバイダー",

    // Overtime Configuration
    configuration: "設定",
    overtimeConfig: "残業設定",
    overtimeCoverage: "カバレッジ",
    activeOvertimeConfigurations:
      "現在、有効な残業設定があります。カバレッジを変更するには、まずこれらの設定を無効にする必要があります。",
    salaryType: "給与タイプ",
    specialWorkDays: "残業日",
    wageIndex: "賃金指数",
    overtimeType: "残業タイプ",
    amount: "金額",
    viewOvertimeConfig: "設定を表示",
    viewAll: "すべて表示",
    employees: "従業員",
    errorEmployeeList:
      "従業員リストの取得中に問題が発生しました。後でもう一度試してください。",
    noEmployeesFound:
      "選択したカスタムグループに関連付けられた従業員が見つかりませんでした。選択したグループに従業員を追加するか、別のグループを選択してみてください。",
    customGroupEmployees: "カスタムグループの従業員",
    addOvertimeConfig: "設定を追加",
    editOvertimeConfig: "設定を編集",
    addCustomGroup: "カスタムグループを追加",
    updateOvertimeCoverage: "カバレッジを更新してもよろしいですか？",
    overtimeConfigSetTo:
      "残業設定は {current} に設定されていますが、プラットフォームの {conflicting} レベル設定と矛盾しています。プラットフォームを {adjust} に調整するか、新しい設定を作成してください。",
    coverageType: "カバレッジタイプ",
    overTimeEmptyText1:
      "管理者向けの残業設定では、従業員の残業がどのように計算・承認・支払われるかを一元的に管理できます。管理者は、組織またはカスタムグループに基づいて適格基準を定義し、計算方法（定額制、スラブなし、またはスラブ制）を選択できます。システムでは、通常日、休日、祝日に対する残業率を会社の方針または法的要件に合わせてカスタマイズできます。",
    overTimeEmptyText2:
      "管理者は、通常勤務日、休日、特別な非労働祝日、および通常の祝日に対して異なる支払い率を設定できます。これにより、労働法に準拠し、公平な従業員の報酬が確保されます。全体として、残業設定は柔軟でポリシー主導、監査対応のフレームワークを提供し、組織全体での残業管理を効率的に行うことができます。",
    overtimeAddSuccess: "残業設定が正常に追加されました",
    overtimeUpdateSuccess: "残業設定が正常に更新されました",
    coverageUpdateSuccess: "残業の適用範囲が正常に更新されました。",
    noAccessMessage: "この操作を実行する権限がありません。",

    // General Settings - Nested structure
    general: {
      // General
      general: "一般",
      generalSettings: "一般設定",

      // Brand Assets
      brandAssets: "ブランド資産",
      companyLogo: "会社ロゴ",
      companyLogoHint:
        "会社ロゴをアップロード（PNG、JPEG、JPG、GIF）。最大サイズ：2MB",
      favicon: "ファビコン",
      faviconHint:
        "ファビコンをアップロード（PNG、JPEG、JPG、GIF、ICO）。最大サイズ：1MB",
      faviconPreview: "ファビコンプレビュー",
      useCompanyLogoAsProductLogo: "会社ロゴを製品ロゴとして使用",
      logo: "ロゴ",

      // Layout and Styling
      layoutAndStyling: "レイアウトとスタイリング",
      colorScheme: "カラースキーム",
      primary: "プライマリ",
      secondary: "セカンダリ",
      hover: "ホバー",
      colorControls: "色の制御",
      primaryColor: "プライマリカラー",
      secondaryColor: "セカンダリカラー",
      hoverColor: "ホバーカラー",
      dataTableHeaderPanel: "データテーブルヘッダーパネル",
      dataTableHeaderText: "データテーブルヘッダーテキスト",

      // Color Picker
      chooseColor: "{colorType}色を選択",
      presetColors: "プリセットカラー",

      // Validation Messages
      fileSizeMustBeLessThan2MB: "ファイルサイズは2MB未満である必要があります",
      fileSizeMustBeLessThan1MB: "ファイルサイズは1MB未満である必要があります",

      // Success Messages
      updatedSuccessfully: "一般設定が正常に更新されました！",

      // Tab Names
      customFields: "カスタムフィールド",
      emailTemplates: "メールテンプレート",

      // Tooltips and Help Text
      appliesAcrossApplication: "アプリケーション全体に適用されます",
      noChangesToUpdate: "更新する変更はありません",
    },

    // Career Page Designer - Nested structure
    careerPage: {
      // Main component
      livePreview: "ライブプレビュー",
      viewOpenings: "求人を見る",

      configurationSavedSuccessfully: "設定が正常に保存されました！",
      failedToUploadAssets: "アセットのアップロードに失敗しました",

      // Brand Assets (reusing from general section where possible)
      careerLogo: "キャリアページロゴ",
      careerLogoHint:
        "キャリアページロゴをアップロード（PNG、JPEG、JPG）。最大サイズ：2MB。",
      bannerImage: "バナー画像",
      filesSelected: "{count}個のファイルが選択されました",
      bannerImageHint: "推奨サイズ：1920×600px。PNG、JPEG、JPGをサポート。",
      logoAlt: "ロゴ",
      careerLogoTooltip:
        "会社ロゴをオーバーライドし、会社のパブリックウェブサイトとブランドに一致するキャリアページのユニークなロゴを提示します。",

      // AI Suggestions
      aiPoweredBannerSuggestions: "AI搭載バナー提案",
      howItWorks: "仕組み",
      aiDescription:
        "あなたのブランドと業界について説明してください。AIがあなたの会社のアイデンティティに合わせたバナー画像を提案します。",
      companyBranding: "会社ブランディング",
      companyBrandingPlaceholder:
        "例：私たちは青と白の配色を持つミニマリストなテックスタートアップです。ロゴはシンプルな幾何学的形状です...",
      industryTrends: "業界トレンド",
      industryTrendsPlaceholder:
        "例：私たちの業界の現在のトレンドは、抽象的なデータ視覚化スタイルの画像と多様なチーム写真を好みます...",
      getSuggestions: "提案を取得",
      aiGeneratedBannerSuggestions: "AI生成バナー提案",
      aiSuggestionsDescription:
        "あなたの会社のアイデンティティに合わせたAI生成バナー提案から選択してください。",
      backToForm: "フォームに戻る",
      applySelected: "選択を適用",

      // Content Customization
      contentCustomization: "コンテンツカスタマイズ",
      bannerOverlayHeading: "バナーオーバーレイ見出し",
      bannerOverlaySubText: "バナーオーバーレイサブテキスト",
      enterBannerHeading: "バナー見出しを入力...",
      enterBannerSubText: "バナーサブテキストを入力...",
      bannerHeading: "バナー見出し",
      bannerSubText: "バナーサブテキスト",
      bannerHeadingDescription:
        "バナー画像にオーバーレイ表示されるメイン見出しテキスト。",
      bannerSubTextDescription:
        "バナー画像の見出しの下に表示されるサポートテキスト。",

      // Text Positioning
      textPosition: "テキスト位置",
      horizontalPosition: "水平位置",
      verticalPosition: "垂直位置",
      left: "左",
      center: "中央",
      right: "右",
      top: "上",
      middle: "中間",
      bottom: "下",
      positionPreview: "位置プレビュー",

      // Typography Controls
      typographyControls: "タイポグラフィコントロール",
      bannerFontFamily: "バナーフォントファミリー",
      bannerHeadingFontSize: "バナー見出しフォントサイズ",
      bannerSubtextFontSize: "バナーサブテキストフォントサイズ",
      bannerFontColor: "バナーフォント色",
      chooseBannerFontColor: "バナーフォント色を選択",

      // Layout and Styling - Banner Overlay
      bannerOverlay: "バナーオーバーレイ",
      bannerOverlayOpacity: "バナーオーバーレイ不透明度",

      // Color Palette Preview
      colorPalettePreview: "カラーパレットプレビュー",
      primaryButtons: "プライマリ（ボタン）",
      secondaryPreview: "セカンダリ（プレビュー）",
      hoverHighlighting: "ホバー（ハイライト）",
      dataTableHeaderPanel: "データテーブルヘッダーパネル",
      dataTableHeaderText: "データテーブルヘッダーテキスト",

      // Career Preview
      companyLogoAlt: "会社ロゴ",
      logoPlaceholder: "200×80",
      openPositions: "募集中のポジション（サンプルデータ）",
      apply: "応募する",

      // Validation and Error Messages
      fileSizeMustBeLessThan2MB: "ファイルサイズは2MB未満である必要があります",
      fileSizeMustBeLessThan1MB: "ファイルサイズは1MB未満である必要があります",
      fileSizeMustBeLessThan3MB: "ファイルサイズは3MB未満である必要があります",
      fileSizeMustBeLessThanMB:
        "ファイルサイズは{size}MB未満である必要があります",
      failedToProcessFile: "{type}の処理に失敗しました。再試行してください。",
      failedToGenerateSuggestions:
        "提案の生成に失敗しました。再試行してください。",
    },
    failedToLoadRecruitmentSettings:
      "採用設定の読み込みに失敗しました。再試行してください。",
    failedToSaveSettings: "設定の保存に失敗しました。再試行してください。",

    /* ----- Recruitment Settings ----- */
    // Section Headers
    generalSettings: "一般設定",
    integrationSettings: "統合設定",
    candidateExperiencePortalConfiguration:
      "候補者エクスペリエンスポータル設定",

    // Field Labels
    jobPostVisibility: "求人投稿の可視性",
    centralisedRecruitment: "集中採用",
    allowModifyInterviewRounds: "求人投稿作成後の面接ラウンド変更を許可",
    equalOpportunityStatement: "候補者応募フォームに表示する機会均等声明",
    enableNewFeatures: "最近導入された機能を有効化",
    enableMppIntegration: "MPP統合を有効化",
    allowEditingJobTitles: "ジョブタイトルの編集を許可",
    automaticallySendSignedDocument: "署名済み文書を自動送信",
    tenantUniqueKey: "テナント固有キー",
    allowCandidatePortalAccessNewApplicant:
      "新規応募者の候補者ポータルアクセスを許可",
    allowBlacklistedCandidatePortalAccess:
      "ブラックリスト候補者のポータルアクセスを許可",
    allowArchivedCandidatePortalAccess:
      "アーカイブ候補者のポータルアクセスを許可",
    otpExpiryDuration: "OTP有効期限（分）",
    candidateApplicationFormType: "候補者応募フォームタイプ",
    allowCandidateMultipleJobs: "候補者の複数求人応募を許可",
    careerPortalFilters: "キャリアポータルフィルター",

    // Tooltips
    jobPostVisibilityTooltip:
      "ユーザーグループまたは組織単位に基づく求人投稿の可視性を定義します。",
    jobPostVisibilityTooltipNoFieldForce:
      "ユーザーグループまたは組織全体に基づく求人投稿の可視性を定義します。",
    centralisedRecruitmentTooltip:
      "採用担当者が組織全体で作成された求人投稿にアクセスし管理することを可能にします。",
    allowModifyInterviewRoundsTooltip:
      "求人投稿後に採用担当者が面接ラウンドを変更することを許可します。",
    enableNewFeaturesTooltip:
      "最近導入された機能をユーザー体験のための一時的に有効にします。",
    equalOpportunityStatementTooltip:
      "多様性、包括性、雇用機会均等に対する組織のコミットメントについて候補者に表示するメッセージをカスタマイズします。",
    enableMppIntegrationTooltip:
      "人員計画（MPP）モジュールからの承認済み求人要求に基づく求人投稿の作成を可能にします。",
    allowEditingJobTitlesTooltip:
      "有効にすると、この機能により、採用担当者は求人要求に対して求人投稿を作成する際にジョブタイトルを編集できます。",
    automaticallySendSignedDocumentTooltip:
      "有効にすると、システムはすべての当事者の署名を受領した後、メール通知に署名済み文書を添付して送信します。",
    tenantUniqueKeyTooltip:
      "外部システムと求人投稿を同期するために使用される一意の識別子。",
    allowCandidatePortalAccessNewApplicantTooltip:
      "候補者がポータルにアクセスしてプロフィールを表示し、詳細を管理し、応募進捗を追跡することを許可します。",
    allowBlacklistedCandidatePortalAccessTooltip:
      "ブラックリスト候補者がポータルにアクセスして拒否理由を確認することを許可",
    allowArchivedCandidatePortalAccessTooltip:
      "完了した採用プロセスの候補者が履歴データにアクセスすることを許可",
    otpExpiryDurationTooltip: "候補者ポータルログイン用のOTPが有効である期間",
    candidateApplicationFormTypeTooltip:
      "シンプルフォームは基本情報を収集；詳細フォームは包括的な候補者プロファイリングを含む",
    allowCandidateMultipleJobsTooltip:
      "候補者がワンクリックで複数のポジションに同時に応募することを許可します。",
    careerPortalFiltersTooltip:
      "求人を閲覧する際に候補者が利用できるフィルター",

    // Placeholders and Messages
    enterTenantUniqueKey: "テナント固有キーを入力",
    thereAreNoChangesToBeUpdated: "更新する変更がありません",
    areYouSureToExitThisForm: "このフォームを終了してもよろしいですか？",
  },
  sp: {
    /* ----- Settings ----- */
    // Module names
    settings: "Configuración",

    // Core HR
    timeOff: "Permiso",
    compOff: "Día compensatorio",
    specialWages: "Salarios especiales",
    overtime: "Horas extra",
    attendanceConfiguration: "Configuración de asistencia",
    geoFencing: "Geoperimetraje y asistencia con selfie",
    holidays: "Días festivos",
    preApprovals: "Preaprobaciones",
    lopRecovery: "Recuperación de días sin sueldo",
    roster: "Turnos",
    roles: "Roles",

    // Data Loss Prevention Settings
    dataLossPrevention: "Prevención de Pérdida de Datos",
    internetAccessControl: "Control de Acceso a Internet",
    locationTracking: "Seguimiento de Ubicación",
    keyLogging: "Registro de Teclas",
    additionalScreenshots: "Capturas de Pantalla Adicionales",
    auditLog: "Registro de Auditoría",

    // Productivity Monitoring
    members: "Miembros",
    reports: "Informes",
    productivityMonitoring: "Monitoreo de Productividad",

    // Common
    status: "Estado",
    enabled: "Habilitado",
    disabled: "Deshabilitado",
    enable: "Habilitar",
    disable: "Deshabilitar",
    active: "Activo",
    inactive: "Inactivo",
    new: "Nuevo",
    added: "Añadido",
    updated: "Actualizado",
    exitFormWarning: "¿Está seguro de salir de este formulario?",
    noChangesToUpdate: "No hay cambios para actualizar",
    confirmExitForm: "¿Está seguro de salir de este formulario?",
    noRecordsFound:
      "No hay {formName} para los filtros/búsquedas seleccionados.",
    ipAddress: "Dirección IP",
    cidr: "CIDR",
    employee: "Empleado",
    employeeName: "Nombre del Empleado",
    employeeId: "ID del Empleado",
    organization: "Organización",
    organizationLevel: "Nivel de Organización",
    employeeLevel: "Nivel de Empleado",
    addedOn: "Agregado el",
    updatedOn: "Actualizado el",
    addedBy: "Agregado por",
    updatedBy: "Actualizado por",
    actions: "Acciones",
    yes: "Sí",
    no: "No",
    save: "Guardar",
    cancel: "Cancelar",
    close: "Cerrar",
    edit: "Editar",
    delete: "Eliminar",
    add: "Agregar",
    addNew: "Agregar Nuevo",
    search: "Buscar",
    filter: "Filtrar",
    resetFilter: "Restablecer Filtro",
    resetFilterSearch: "Restablecer Filtro/Búsqueda",
    export: "Exportar",
    refresh: "Actualizar",
    setAll: "Establecer todo",
    retry: "Reintentar",
    changedSettings: "Ha cambiado {count} configuración(es)",
    addConfiguration: "Agregar Configuración",
    itemPerPage: "Elementos por página:",
    pageText: "{start}-{end} de {total}",
    apply: "Aplicar",
    reset: "Restablecer",
    designation: "Cargo",
    department: "Departamento",

    // Internet Access Control
    enableInternetAccessControl: "Habilitar Control de Acceso a Internet",
    notifyInternetAccessViolation: "Notificar Violación de Acceso a Internet",
    internetAccessUpdateFrequency:
      "Frecuencia de Actualización de Acceso a Internet",
    overrideInternetAccess: "Anular Acceso a Internet",
    blockedDomains: "Dominios Bloqueados",
    whitelistedDomains: "Dominios en Lista Blanca",
    domainName: "Nombre de Dominio",
    category: "Categoría",
    productive: "Productivo",
    unproductive: "Improductivo",
    neutral: "Neutral",
    addBlockedDomain: "Agregar Dominio Bloqueado",
    addWhitelistedDomain: "Agregar Dominio a Lista Blanca",
    domainCategory: "Categoría de Dominio",
    tooltipDomainContent:
      "El administrador puede anular el control de acceso a Internet a nivel de organización con un control de acceso a nivel de empleado editando la configuración a continuación",
    notifyButtonContent:
      "Cuando el control de acceso a Internet está configurado como 'No', tenga en cuenta que la notificación de acceso a Internet se establecerá como 'No' y no se podrá modificar.",
    accessDeniedUpdateMessage:
      "El empleado conectado no tiene acceso de actualización",
    saveOrCancelChanges: "Guarde o cancele los cambios antes de continuar",
    enableInternetAccessMessage:
      "Habilite la función de control de acceso a Internet para permitir la adición o eliminación de sitios web de la lista bloqueada.",

    // Internet Access Control Modal
    websitesBlockedUnblocked: "Sitios web (Bloqueados/Desbloqueados)",
    appsBlockedUnblocked: "Aplicaciones (Bloqueadas/Desbloqueadas)",
    customGroup: "Grupo personalizado",
    selectCustomGroup: "Seleccionar grupo personalizado",
    copyClassificationFrom: "Copiar la clasificación de",
    copyNow: "Copiar ahora",
    wildcardConfiguration: "Configuración de comodines",
    blockAccess: "Bloquear acceso",
    back: "Volver",
    followingWebsitesBlocked:
      "Los siguientes sitios web serán bloqueados para el acceso.",
    noSuchDomain: "No existe tal dominio, o ya está bloqueado.",
    thereAreNoBlockedWebsites: "No hay sitios web bloqueados",
    addMore: "Agregar más",
    changesHaveBeenDone: "Se han realizado {count} cambio(s)",
    domainRemovedSuccess: "Dominio eliminado de la lista de bloqueo con éxito.",
    domainStatusUpdatedSuccess: "Estado del dominio actualizado con éxito",
    domainAddedSuccess: "Dominio agregado con éxito a la lista de bloqueo.",
    clearAll: "Borrar todo",
    all: "Todos",
    blocked: "Bloqueado",
    unblocked: "Desbloqueado",
    block: "Bloquear",
    unblock: "Desbloquear",
    domainStatusCannotBeChanged:
      "El estado del dominio no se puede cambiar ya que se estableció a través de la configuración de comodines",
    noItemsToCategorizePre: "No tiene ningún ",
    noItemsToCategorizePost: " para categorizar.",
    moreDetails: "Más detalles",

    // Location Tracking
    locationTrackingSettings: "Configuración de Seguimiento de Ubicación",
    enableLocationTracking: "Habilitar Seguimiento de Ubicación",
    workLocation: "Ubicación de Trabajo",
    ipRange: "Rango de IP",
    addWorkLocation: "Agregar Ubicación de Trabajo",
    editWorkLocation: "Editar Ubicación de Trabajo",
    locationName: "Nombre de Ubicación",
    locationTrackingSettingsUpdatedSuccess:
      "Configuración de seguimiento de ubicación actualizada correctamente",
    workLocationAddedSuccess: "Ubicación de trabajo agregada correctamente",
    workLocationUpdatedSuccess:
      "Ubicación de trabajo actualizada correctamente",
    locationTrackingDescription:
      "Rastrear ubicaciones de empleados basadas en direcciones IP",
    ipRangeFormat: "Formato de Rango IP (ej: ***********-*************)",
    locationTrackingLongDescription:
      "Para garantizar una categorización precisa de la actividad del usuario en el panel de ubicación, asigne un rango específico de direcciones IP a su red de oficina.",
    ipAddressTypePublic: "Tipo de dirección IP: Pública (IP de Internet)",
    addressFormatIPv4: "Formato de dirección: Solo IPv4",
    ipRangeSpecify:
      "Rango: Especifique la dirección IP o rango (incluyendo máscara de subred)",
    ipAddressImportantNote:
      "Importante: Las direcciones IP asignadas a actividades pasadas no se actualizarán retrospectivamente.",
    locationTrackingNote1:
      "La configuración de Seguimiento de Ubicación puede ayudar a supervisar y gestionar el uso de la red registrando las direcciones IP de los dispositivos conectados a la red de su empresa",
    locationTrackingNote2:
      "La configuración típicamente incluye opciones para habilitar o deshabilitar el seguimiento, agregar nuevas ubicaciones de trabajo y direcciones IP",

    // Key Logging
    keyLoggingSettings: "Configuración de Registro de Teclas",
    captureControlKeys: "Capturar Teclas de Control",
    keyStrokeCount: "Conteo de Pulsaciones",
    applicationName: "Nombre de la Aplicación",
    websites: "Sitios web",
    windowTitle: "Título de la Ventana",

    // Additional Screenshots
    additionalScreenshotsSettings:
      "Configuración de Capturas de Pantalla Adicionales",
    captureScreenshot: "Capturar Pantalla",
    screenshotSchedule: "Programación de Capturas",
    fixed: "Fijo",
    random: "Aleatorio",
    screenshotFrequency: "Frecuencia de Capturas",
    screenshotFrequencyType: "Tipo de Frecuencia de Capturas",
    screenshotsPerFrequency: "Capturas por Frecuencia",
    additionalScreenshotsPerFrequency: "Capturas Adicionales por Frecuencia",
    screenshotBlur: "Desenfoque de Capturas",
    minutes: "Minutos",
    hours: "Horas",

    // Messages
    confirmationHeading: "¿Está seguro de cambiar el estado?",
    settingsUpdatedSuccess: "Configuración actualizada con éxito",
    locationTrackingUpdatedSuccess:
      "Configuración de seguimiento de ubicación actualizada con éxito",

    keyLoggingUpdatedSuccess:
      "Configuración de registro de teclas actualizada con éxito",
    screenshotsUpdatedSuccess:
      "Configuración de capturas de pantalla actualizada con éxito",
    accessDeniedMessage: "No tiene acceso para realizar esta acción",

    // Help text
    internetAccessControlHelp:
      "El Control de Acceso a Internet ayuda a monitorear y controlar el acceso a sitios web y dominios en toda su organización.",
    locationTrackingHelp:
      "El Seguimiento de Ubicación ayuda a identificar si los empleados están trabajando desde la oficina o ubicaciones remotas según las direcciones IP.",
    keyLoggingHelp:
      "El Registro de Teclas rastrea la actividad del teclado para monitorear la productividad y detectar posibles fugas de datos.",
    keyLoggingDescription:
      "Habilitar el registro de teclas capturará todas las pulsaciones de teclas realizadas por los usuarios mientras interactúan con la aplicación, ayudando a monitorear la actividad del usuario. Estos datos pueden proporcionar información sobre patrones de uso y garantizar el cumplimiento de las políticas de seguridad.",
    additionalScreenshotsHelp:
      "Las Capturas de Pantalla Adicionales permiten capturar capturas adicionales para monitorear la actividad de los empleados más allá de los intervalos regulares.",
    additionalScreenshotsNote1:
      "Las capturas de pantalla periódicas o activadas se capturan para dar contexto visual a los registros, ayudando a verificar las tareas que se están realizando realmente.",
    additionalScreenshotsNote2:
      "Toma capturas de pantalla automáticas a intervalos o basadas en activadores específicos (por ejemplo, abrir aplicaciones o sitios web específicos).",

    // Employee Number Series
    employeeNumberSeries: "Série de numéros d'employé",
    employeeNumberSeriesSettings: "Paramètres de série de numéros d'employé",
    enableEmployeeNumberSeries: "Activer la série de numéros d'employé",
    prefix: "Préfixe",
    suffix: "Suffixe",
    noOfDigits: "Nombre de chiffres",
    nextNumber: "Prochain numéro",
    viewEmployeeNumberSeries: "Voir la série de numéros d'employé",
    addEmployeeNumberSeries: "Ajouter une série de numéros d'employé",
    editEmployeeNumberSeries: "Modifier la série de numéros d'employé",
    serviceProvider: "Fournisseur de services",

    // Overtime Configuration
    configuration: "Configuración",
    overtimeConfig: "Configuración de horas extra",
    overtimeCoverage: "Cobertura",
    activeOvertimeConfigurations:
      "Actualmente hay configuraciones activas de horas extra. Para modificar la cobertura, primero debes inactivar estas configuraciones.",
    salaryType: "Tipo de salario",
    specialWorkDays: "Horas extras en",
    wageIndex: "Índice de salario",
    overtimeType: "Tipo de horas extras",
    amount: "Monto",
    viewOvertimeConfig: "Ver Configuración",
    viewAll: "Ver todo",
    employees: "Empleados",
    errorEmployeeList:
      "Algo salió mal al recuperar la lista de empleados. Por favor, inténtalo de nuevo.",
    noEmployeesFound:
      "Parece que no hay empleados asociados con el grupo personalizado seleccionado. Agregue algunos empleados bajo el grupo seleccionado o intente elegir otro grupo.",
    customGroupEmployees: "Empleado(s) del grupo personalizado",
    addOvertimeConfig: "Agregar una configuración",
    editOvertimeConfig: "Editar Configuración",
    addCustomGroup: "Agregar grupo personalizado",
    updateOvertimeCoverage:
      "¿Estás seguro de que deseas actualizar la cobertura?",
    overtimeConfigSetTo:
      "La configuración de horas extra está establecida en {current}, lo que entra en conflicto con el ajuste de nivel {conflicting} de la plataforma. Por favor, ajusta la plataforma a {adjust} o crea una nueva configuración.",
    coverageType: "Tipo de cobertura",
    overTimeEmptyText1:
      "La configuración de horas extra para el administrador permite un control centralizado sobre cómo se calculan, aprueban y pagan las horas extra de los empleados. El administrador puede definir los criterios de elegibilidad según la organización o un grupo personalizado, y elegir el método de cálculo: tarifa fija, sin escalas o con escalas. El sistema permite personalizar las tarifas de horas extra para días laborables normales, días de descanso y días festivos, alineándose con la política de la empresa o los requisitos legales.",
    overTimeEmptyText2:
      "El administrador puede configurar diferentes porcentajes de pago para los días laborables regulares, días de descanso, días festivos especiales no laborables y días festivos regulares. Esto asegura que los empleados sean compensados de manera justa según las leyes laborales aplicables. En general, la configuración de horas extra ofrece un marco flexible, basado en políticas y conforme a auditorías para gestionar eficientemente las horas extra en toda la organización.",
    overtimeAddSuccess: "Configuración de horas extra agregada con éxito",
    overtimeUpdateSuccess: "Configuración de horas extra actualizada con éxito",
    coverageUpdateSuccess:
      "La cobertura de horas extra se actualizó correctamente.",
    noAccessMessage: "No tienes acceso para realizar esta acción.",

    // General Settings - Nested structure
    general: {
      // General
      general: "General",
      generalSettings: "Configuración General",

      // Brand Assets
      brandAssets: "Activos de Marca",
      companyLogo: "Logo de la Empresa",
      companyLogoHint:
        "Subir logo de la empresa (PNG, JPEG, JPG). Tamaño máx: 2MB",
      favicon: "Favicon",
      faviconHint: "Subir favicon (PNG, JPEG, JPG, ICO). Tamaño máx: 1MB",
      faviconPreview: "Vista Previa del Favicon",
      useCompanyLogoAsProductLogo:
        "Usar Logo de la Empresa como Logo del Producto",
      logo: "Logo",

      // Layout and Styling
      layoutAndStyling: "Diseño y Estilo",
      colorScheme: "Esquema de Colores",
      primary: "Primario",
      secondary: "Secundario",
      hover: "Hover",
      colorControls: "Controles de Color",
      primaryColor: "Color Primario",
      secondaryColor: "Color Secundario",
      hoverColor: "Color de Hover",
      dataTableHeaderPanel: "Panel de Encabezado de Tabla de Datos",
      dataTableHeaderText: "Texto de Encabezado de Tabla de Datos",

      // Color Picker
      chooseColor: "Elegir Color {colorType}",
      presetColors: "Colores Predefinidos",

      // Validation Messages
      fileSizeMustBeLessThan2MB: "El tamaño del archivo debe ser menor a 2MB",
      fileSizeMustBeLessThan1MB: "El tamaño del archivo debe ser menor a 1MB",

      // Success Messages
      updatedSuccessfully: "¡Configuración general actualizada exitosamente!",

      // Tab Names
      customFields: "Campos Personalizados",
      emailTemplates: "Plantillas de Email",

      // Tooltips and Help Text
      appliesAcrossApplication: "Se aplica en toda la aplicación",
      noChangesToUpdate: "No hay cambios para actualizar",
    },

    // Career Page Designer - Nested structure
    careerPage: {
      // Main component
      livePreview: "Vista Previa en Vivo",
      viewOpenings: "Ver Vacantes",

      configurationSavedSuccessfully: "¡Configuración guardada exitosamente!",
      failedToUploadAssets: "Error al cargar los recursos",

      // Brand Assets (reusing from general section where possible)
      careerLogo: "Logo de la Página de Carrera",
      careerLogoHint:
        "Subir logo de la página de carrera (PNG, JPEG, JPG). Tamaño máx: 2MB.",
      bannerImage: "Imagen de Banner",
      filesSelected: "{count} archivos seleccionados",
      bannerImageHint:
        "Tamaño recomendado: 1920×600px. Soporta PNG, JPEG, JPG.",
      logoAlt: "Logo",
      careerLogoTooltip:
        "Reemplaza el logo de la empresa y presenta un logo único para la página de carrera en alineación con el sitio web público de tu empresa y tu marca.",

      // AI Suggestions
      aiPoweredBannerSuggestions: "Sugerencias de Banner con IA",
      howItWorks: "Cómo funciona",
      aiDescription:
        "Describe tu marca e industria. Nuestra IA sugerirá imágenes de banner adaptadas a la identidad de tu empresa.",
      companyBranding: "Marca de la Empresa",
      companyBrandingPlaceholder:
        "ej., Somos una startup tecnológica minimalista con un esquema de colores azul y blanco. Nuestro logo es una forma geométrica simple...",
      industryTrends: "Tendencias de la Industria",
      industryTrendsPlaceholder:
        "ej., Las tendencias actuales en nuestra industria favorecen imágenes abstractas de estilo visualización de datos y fotos de equipos diversos...",
      getSuggestions: "Obtener Sugerencias",
      aiGeneratedBannerSuggestions: "Sugerencias de Banner Generadas por IA",
      aiSuggestionsDescription:
        "Elige entre nuestras sugerencias de banner generadas por IA adaptadas a la identidad de tu empresa.",
      backToForm: "Volver al Formulario",
      applySelected: "Aplicar Seleccionado",

      // Content Customization
      contentCustomization: "Personalización de Contenido",
      bannerOverlayHeading: "Encabezado de Superposición de Banner",
      bannerOverlaySubText: "Subtexto de Superposición de Banner",
      enterBannerHeading: "Ingresa el encabezado del banner...",
      enterBannerSubText: "Ingresa el subtexto del banner...",
      bannerHeading: "Encabezado de Banner",
      bannerSubText: "Subtexto de Banner",
      bannerHeadingDescription:
        "Texto de encabezado principal que aparecerá superpuesto en tu imagen de banner.",
      bannerSubTextDescription:
        "Texto de apoyo que aparecerá debajo del encabezado en tu imagen de banner.",

      // Text Positioning
      textPosition: "Posición del Texto",
      horizontalPosition: "Posición Horizontal",
      verticalPosition: "Posición Vertical",
      left: "Izquierda",
      center: "Centro",
      right: "Derecha",
      top: "Arriba",
      middle: "Medio",
      bottom: "Abajo",
      positionPreview: "Vista Previa de Posición",

      // Typography Controls
      typographyControls: "Controles de Tipografía",
      bannerFontFamily: "Familia de Fuente del Banner",
      bannerHeadingFontSize: "Tamaño de Fuente del Encabezado del Banner",
      bannerSubtextFontSize: "Tamaño de Fuente del Subtexto del Banner",
      bannerFontColor: "Color de Fuente del Banner",
      chooseBannerFontColor: "Elegir Color de Fuente del Banner",

      // Layout and Styling - Banner Overlay
      bannerOverlay: "Superposición de Banner",
      bannerOverlayOpacity: "Opacidad de Superposición de Banner",

      // Color Palette Preview
      colorPalettePreview: "Vista Previa de Paleta de Colores",
      primaryButtons: "Primario (Botones)",
      secondaryPreview: "Secundario (Vista Previa)",
      hoverHighlighting: "Hover (Resaltado)",
      dataTableHeaderPanel: "Panel de Encabezado de Tabla de Datos",
      dataTableHeaderText: "Texto de Encabezado de Tabla de Datos",

      // Career Preview
      companyLogoAlt: "Logo de la Empresa",
      logoPlaceholder: "200×80",
      openPositions: "Posiciones Abiertas (datos de muestra)",
      apply: "Aplicar",

      // Validation and Error Messages
      fileSizeMustBeLessThan2MB: "El tamaño del archivo debe ser menor a 2MB",
      fileSizeMustBeLessThan1MB: "El tamaño del archivo debe ser menor a 1MB",
      fileSizeMustBeLessThan3MB: "El tamaño del archivo debe ser menor a 3MB",
      fileSizeMustBeLessThanMB:
        "El tamaño del archivo debe ser menor a {size}MB",
      failedToProcessFile:
        "Error al procesar {type}. Por favor, inténtalo de nuevo.",
      failedToGenerateSuggestions:
        "Error al generar sugerencias. Por favor, inténtalo de nuevo.",
    },
    /* ----- Recruitment Settings ----- */
    failedToLoadRecruitmentSettings:
      "Error al cargar la Configuración de Reclutamiento. Por favor, inténtalo de nuevo.",
    failedToSaveSettings:
      "Error al guardar la configuración. Por favor, inténtalo de nuevo.",

    // Section Headers
    generalSettings: "Configuración General",
    integrationSettings: "Configuración de Integración",
    candidateExperiencePortalConfiguration:
      "Configuración del Portal de Experiencia del Candidato",

    // Field Labels
    jobPostVisibility: "Visibilidad de Publicación de Trabajo",
    centralisedRecruitment: "Reclutamiento Centralizado",
    allowModifyInterviewRounds:
      "Permitir modificar las rondas de entrevista después de la creación de la publicación de trabajo",
    equalOpportunityStatement:
      "Declaración de igualdad de oportunidades para presentar en el formulario de solicitud del candidato",
    enableNewFeatures: "Habilitar Funciones Recientemente Introducidas",
    enableMppIntegration: "Habilitar Integración MPP",
    allowEditingJobTitles: "Permitir la edición de los títulos de trabajo",
    automaticallySendSignedDocument:
      "Enviar automáticamente el documento firmado",
    tenantUniqueKey: "Clave Única del Inquilino",
    allowCandidatePortalAccessNewApplicant:
      "Permitir Acceso al Portal del Candidato para Nuevo Solicitante",
    allowBlacklistedCandidatePortalAccess:
      "Permitir Acceso al Portal del Candidato en Lista Negra",
    allowArchivedCandidatePortalAccess:
      "Permitir Acceso al Portal del Candidato Archivado",
    otpExpiryDuration: "Duración de Expiración OTP (en minutos)",
    candidateApplicationFormType:
      "Tipo de Formulario de Solicitud del Candidato",
    allowCandidateMultipleJobs:
      "Permitir al candidato aplicar a múltiples trabajos",
    careerPortalFilters: "Filtros del Portal de Carrera",

    // Tooltips
    jobPostVisibilityTooltip:
      "Define la visibilidad de las publicaciones de trabajo basada en el grupo de usuarios o unidad organizacional.",
    jobPostVisibilityTooltipNoFieldForce:
      "Define la visibilidad de las publicaciones de trabajo basada en el grupo de usuarios o toda la organización.",
    centralisedRecruitmentTooltip:
      "Permite a los reclutadores acceder y gestionar publicaciones de trabajo creadas en toda la organización.",
    allowModifyInterviewRoundsTooltip:
      "Permite a los reclutadores modificar las rondas de entrevista después de la publicación del trabajo.",
    equalOpportunityStatementTooltip:
      "Personaliza el mensaje mostrado a los candidatos sobre el compromiso de su organización con la diversidad, inclusión e igualdad de oportunidades de empleo.",
    enableNewFeaturesTooltip:
      "Habilita las funciones recientemente introducidas para la experiencia del usuario antes de activarlas permanentemente.",
    enableMppIntegrationTooltip:
      "Permite que las publicaciones de trabajo se creen basadas en requisiciones de trabajo aprobadas del módulo de Planificación de Personal (MPP).",
    allowEditingJobTitlesTooltip:
      "Cuando se habilita, esto permite a los reclutadores modificar el título del trabajo al crear una publicación de trabajo con respecto a una solicitud de requisición de trabajo.",
    automaticallySendSignedDocumentTooltip:
      "Si está habilitado, el sistema enviará el documento firmado como un archivo adjunto en la notificación por correo electrónico después de recibir la firma de todas las partes.",
    tenantUniqueKeyTooltip:
      "Identificador único utilizado para sincronizar publicaciones de trabajo con sistemas externos.",
    allowCandidatePortalAccessNewApplicantTooltip:
      "Otorga a los candidatos acceso al portal para ver su perfil, gestionar detalles y rastrear el progreso de la solicitud.",
    allowBlacklistedCandidatePortalAccessTooltip:
      "Permite a los candidatos en lista negra acceder al portal y ver las razones de rechazo",
    allowArchivedCandidatePortalAccessTooltip:
      "Permite a los candidatos de procesos de contratación completados acceder a sus datos históricos",
    otpExpiryDurationTooltip:
      "Duración durante la cual el OTP permanece válido para el inicio de sesión del portal del candidato",
    candidateApplicationFormTypeTooltip:
      "El formulario simple recopila información básica; el formulario detallado incluye perfilado integral del candidato",
    allowCandidateMultipleJobsTooltip:
      "Permite a los candidatos aplicar a múltiples posiciones simultáneamente con un solo clic.",
    careerPortalFiltersTooltip:
      "Filtros disponibles para los candidatos al navegar por las ofertas de trabajo",

    // Placeholders and Messages
    enterTenantUniqueKey: "Ingrese la clave única del inquilino",
    thereAreNoChangesToBeUpdated: "No hay cambios para actualizar",
    areYouSureToExitThisForm: "¿Está seguro de salir de este formulario?",
  },
};
