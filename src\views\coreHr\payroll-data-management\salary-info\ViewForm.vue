<template>
  <v-overlay
    v-model="showOverlay"
    class="d-flex justify-end"
    @click:outside="$emit('close-form')"
  >
    <v-card height="100vh" :width="componentWidth">
      <v-card-title
        class="d-flex justify-space-between align-center bg-primary"
      >
        <div class="text-h6">View {{ formName }}</div>
        <v-btn
          icon="fas fa-times"
          variant="text"
          @click="$emit('close-form')"
          color="white"
        ></v-btn>
      </v-card-title>
      <v-card-text v-if="isLoading">
        <div v-for="i in 3" :key="i" class="mt-4">
          <v-skeleton-loader
            ref="skeleton2"
            type="list-item-avatar"
            class="mx-auto"
          ></v-skeleton-loader>
        </div>
      </v-card-text>
      <v-card-text
        v-else
        style="height: calc(100vh - 100px); overflow-y: scroll"
      >
        <v-card outlined class="pa-3 mt-2">
          <v-row dense>
            <v-col class="d-flex justify-space-between">
              <h3 v-if="formId === 206" class="text-primary text-center">
                {{ selectedItem?.Template_Name }}
              </h3>
              <p v-if="formId !== 206" class="text-h6 text-primary">
                {{
                  selectedItem?.Employee_Name +
                  " - " +
                  selectedItem?.User_Defined_EmpId
                }}
              </p>
              <v-btn
                v-if="formId == 207 || formId == 206"
                variant="text"
                color="primary"
                density="compact"
                @click="$emit('open-edit-form', selectedItem)"
              >
                <span class="mr-2">{{ "Edit" }}</span>
                <v-icon size="15">fas fa-edit</v-icon>
              </v-btn>
            </v-col>
          </v-row>
          <v-row dense class="d-flex align-center">
            <v-col v-if="formId == 207" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Annual CTC</p>
              <span class="text-subtitle-1 font-weight-regular"
                >{{ payrollCurrency }}
                {{
                  checkNullValue(salaryDetails?.Annual_CTC?.toFixed(2))
                }}</span
              >
              <span class="text-caption text-grey-darken-1"> per annum</span>
            </v-col>
            <v-col v-if="formId == 207" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Monthly CTC</p>
              <span class="text-subtitle-1 font-weight-regular"
                >{{ payrollCurrency }}
                {{ (salaryDetails?.Annual_CTC / 12).toFixed(2) || 0 }}
              </span>
            </v-col>
            <v-col v-if="formId == 360" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Revision Type</p>
              <span class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(salaryDetails?.revisionType) }}</span
              >
            </v-col>
            <v-col v-if="formId == 360" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Effective Month</p>
              <span class="text-subtitle-1 font-weight-regular">{{
                checkNullValue(selectedItem?.Effective_Month)
              }}</span>
            </v-col>
            <v-col v-if="formId == 360" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Payout Month</p>
              <span class="text-subtitle-1 font-weight-regular">
                {{ checkNullValue(salaryDetails?.Payout_Month) }}</span
              >
            </v-col>
            <v-col v-if="formId == 207" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Effective From</p>
              <span class="text-subtitle-1 font-weight-regular">{{
                checkNullValue(selectedItem?.Effective_From)
              }}</span>
            </v-col>
            <v-col v-if="formId == 360" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Previous CTC</p>
              <span class="text-subtitle-1 font-weight-regular"
                >{{ payrollCurrency }}
                {{ checkNullValue(salaryDetails?.previousCtc) }}</span
              >
              <span class="text-caption text-grey-darken-1"> per annum</span>
            </v-col>
            <v-col v-if="formId == 360" cols="12" sm="4">
              <p class="text-subtitle-1 text-grey-darken-1">Revised CTC</p>
              <span class="text-subtitle-1 font-weight-regular"
                >{{ payrollCurrency }}
                {{ salaryDetails?.Annual_CTC?.toFixed(2) || 0 }}</span
              >
              <span class="text-caption text-grey-darken-1"> per annum</span>
            </v-col>
          </v-row>
        </v-card>
        <v-card class="pa-2 mt-3" v-if="Object.keys(salaryDetails).length">
          <v-row>
            <v-col cols="6" class="bg-grey-lighten-3">Components</v-col>
            <v-col class="bg-grey-lighten-3 text-end">Monthly</v-col>
            <v-col class="bg-grey-lighten-3 text-end">
              <div class="pr-5">Annually</div>
            </v-col>
          </v-row>
          <v-row class="mt-2">
            <v-col class="font-weight-bold text-subtitle-1 no-padding">
              Earnings
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="6" class="text-subtitle-2 no-padding">
              {{ salaryDetails.basicPayName }}
            </v-col>
            <v-col class="text-subtitle-2 no-padding text-end">
              {{ parseFloat(salaryDetails?.Amount)?.toFixed(2) || 0 }}
            </v-col>
            <v-col class="text-subtitle-2 no-padding text-end">
              <div class="pr-5">
                {{
                  salaryDetails.Amount
                    ? (salaryDetails.Amount * 12).toFixed(2)
                    : 0
                }}
              </div>
            </v-col>
          </v-row>
          <div
            v-if="salaryDetails.allowances?.['allowanceArray'].length"
            class="mt-3"
          >
            <v-row
              v-for="(innerItems, index) in salaryDetails.allowances[
                'allowanceArray'
              ]"
              :key="index"
            >
              <v-col cols="6" class="text-subtitle-2 no-padding">
                {{ innerItems.Allowance_Name }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{ innerItems.Amount?.toFixed(2) || 0 }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ (innerItems.Amount * 12).toFixed(2) || 0 }}
                </div>
              </v-col>
            </v-row>
          </div>
          <div
            v-if="salaryDetails.allowances?.['fixedAllowanceArray'].length"
            class="mt-3"
          >
            <v-row
              v-for="(innerItems, index) in salaryDetails.allowances[
                'fixedAllowanceArray'
              ]"
              :key="index"
            >
              <v-col cols="6" class="text-subtitle-2 no-padding">
                {{ innerItems.Allowance_Name }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{ innerItems.Amount?.toFixed(2) || 0 }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ (innerItems.Amount * 12).toFixed(2) || 0 }}
                </div>
              </v-col>
            </v-row>
          </div>
          <v-divider class="mt-4" thickness="3" color="black"></v-divider>
          <div
            v-for="(item, index) in [
              'reimbursementArray',
              'flexiBenefitPlanArray',
            ]"
            :key="index"
          >
            <div v-if="salaryDetails.allowances[item].length">
              <v-row class="mt-2">
                <v-col class="font-weight-bold text-subtitle-1 no-padding">
                  {{ getGroupName(item) }}
                </v-col>
              </v-row>
              <v-row
                v-for="(innerItems, index) in salaryDetails.allowances[item]"
                :key="index"
              >
                <v-col cols="6" class="text-subtitle-2 no-padding">
                  {{ innerItems.Allowance_Name }}
                </v-col>
                <v-col class="text-subtitle-2 no-padding text-end">
                  {{ innerItems.Amount?.toFixed(2) || 0 }}
                </v-col>
                <v-col class="text-subtitle-2 no-padding text-end">
                  <div class="pr-5">
                    {{ (innerItems.Amount * 12).toFixed(2) || 0 }}
                  </div>
                </v-col>
              </v-row>
              <v-divider class="mt-4" thickness="3" color="black"></v-divider>
            </div>
          </div>
          <div class="mt-1">
            <v-row>
              <v-col cols="6" class="no-padding"> Gross Salary </v-col>
              <v-col class="no-padding text-end">
                {{ salaryDetails.Monthly_Gross_Salary?.toFixed(2) || 0 }}
              </v-col>
              <v-col class="no-padding text-end">
                <div class="pr-5">
                  {{ salaryDetails.Annual_Gross_Salary?.toFixed(2) || 0 }}
                </div>
              </v-col>
            </v-row>
            <v-divider class="mt-4" thickness="3" color="black"></v-divider>
          </div>
          <div
            v-if="salaryDetails.allowances?.['bonusArray'].length"
            class="mt-3"
          >
            <v-row class="mt-2">
              <v-col class="font-weight-bold text-subtitle-1 no-padding">
                Bonus
              </v-col>
            </v-row>
            <v-row
              v-for="(innerItems, index) in salaryDetails.allowances[
                'bonusArray'
              ]"
              :key="index"
            >
              <v-col
                cols="6"
                class="text-subtitle-2 no-padding d-flex align-center"
              >
                <span class="mr-2">{{ innerItems.Allowance_Name }}</span>
                <div
                  class="text-caption px-1 d-flex align-center justify-center text-grey-darken-1"
                  style="width: max-content"
                >
                  ({{
                    innerItems.Amount && innerItems.Period
                      ? innerItems.Amount
                      : 0
                  }}
                  {{ innerItems.Period }})
                </div>
                <v-tooltip
                  v-if="
                    getBonusText &&
                    (innerItems.Allowance_Type?.toLowerCase() ===
                      'percentage' ||
                      innerItems.Allowance_Type?.toLowerCase() === 'variable')
                  "
                  :text="getBonusText"
                >
                  <template v-slot:activator="{ props }">
                    <span class="custom-info-icon">
                      <v-icon color="grey-lighten-1" size="10" v-bind="props"
                        >fas fa-info</v-icon
                      >
                    </span>
                  </template>
                </v-tooltip>
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                {{
                  (innerItems.Amount / periodMap[innerItems.Period][0]).toFixed(
                    2
                  ) || 0
                }}
              </v-col>
              <v-col class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{
                    (
                      innerItems.Amount * periodMap[innerItems.Period][1]
                    ).toFixed(2) || 0
                  }}
                </div>
              </v-col>
            </v-row>
            <v-divider class="mt-4" thickness="3" color="black"></v-divider>
          </div>
          <div v-if="salaryDetails.retirals?.length" class="mb-2">
            <v-row class="mt-2">
              <v-col class="font-weight-bold text-subtitle-1 no-padding">
                Retiral
              </v-col>
            </v-row>
            <v-row
              v-for="(innerItems, index) in salaryDetails.retirals"
              :key="index"
            >
              <v-col
                cols="6"
                class="text-subtitle-2 no-padding d-flex align-center"
              >
                <span class="mr-2">{{
                  innerItems.Retirals_Name?.toLowerCase() === "insurance"
                    ? innerItems.Insurance_Name
                    : getCustomFormName(innerItems.Form_Id)
                    ? getCustomFormName(innerItems.Form_Id)
                    : innerItems.Retirals_Name
                }}</span>
                <v-tooltip
                  v-if="
                    getCustomText(innerItems.Allowance_Ids) &&
                    (innerItems.Retirals_Type?.toLowerCase() === 'percentage' ||
                      innerItems.Retirals_Type?.toLowerCase() === 'variable' ||
                      innerItems.Form_Id === 110)
                  "
                  :text="getCustomText(innerItems.Allowance_Ids)"
                >
                  <template v-slot:activator="{ props }">
                    <span class="custom-info-icon">
                      <v-icon color="grey-lighten-1" size="10" v-bind="props"
                        >fas fa-info</v-icon
                      >
                    </span>
                  </template>
                </v-tooltip>
                <div
                  v-if="innerItems.Insurance_Type?.toLowerCase() == 'fixed'"
                  class="text-caption px-1 d-flex align-center justify-center text-grey-darken-1"
                  style="width: max-content"
                >
                  ({{ innerItems.Payment_Frequency }})
                </div>
              </v-col>
              <v-col
                v-if="innerItems.Insurance_Type?.toLowerCase() == 'fixed'"
                class="text-subtitle-2 no-padding text-end"
              >
                {{
                  innerItems.Amount
                    ? (
                        innerItems.Amount /
                        periodMap[innerItems.Payment_Frequency][0]
                      ).toFixed(2)
                    : 0
                }}
              </v-col>
              <v-col
                v-else-if="innerItems.Form_Id === 110"
                class="text-subtitle-2 no-padding text-end"
              >
                {{ (innerItems.Amount / 12).toFixed(2) }}
              </v-col>
              <v-col v-else class="text-subtitle-2 no-padding text-end">
                {{
                  innerItems.Amount
                    ? parseFloat(innerItems.Amount).toFixed(2)
                    : 0
                }}
              </v-col>
              <v-col
                v-if="innerItems.Insurance_Type?.toLowerCase() == 'fixed'"
                class="text-subtitle-2 no-padding text-end"
              >
                <div class="pr-5">
                  {{
                    innerItems.Amount
                      ? (
                          innerItems.Amount *
                          periodMap[innerItems.Payment_Frequency][1]
                        ).toFixed(2)
                      : 0
                  }}
                </div>
              </v-col>
              <v-col
                v-else-if="innerItems.Form_Id === 110"
                class="text-subtitle-2 no-padding text-end"
              >
                <div class="pr-5">
                  {{
                    innerItems.Amount
                      ? parseFloat(innerItems.Amount).toFixed(2)
                      : 0
                  }}
                </div>
              </v-col>
              <v-col v-else class="text-subtitle-2 no-padding text-end">
                <div class="pr-5">
                  {{ (innerItems.Amount * 12).toFixed(2) }}
                </div>
              </v-col>
            </v-row>
          </div>
        </v-card>
        <v-card class="pa-2 mt-3">
          <v-row>
            <v-col cols="6" class="bg-grey-lighten-4"
              >Cost to Company (CTC)</v-col
            >
            <v-col class="bg-grey-lighten-4 text-end">
              {{ payrollCurrency }}
              {{
                salaryDetails?.Annual_CTC
                  ? (salaryDetails?.Annual_CTC / 12).toFixed(2)
                  : 0
              }}
            </v-col>
            <v-col class="bg-grey-lighten-4 text-end">
              <div class="pr-5">
                {{ payrollCurrency }}
                {{ checkNullValue(salaryDetails?.Annual_CTC?.toFixed(2)) }}
              </div>
            </v-col>
          </v-row>
        </v-card>
        <div
          v-if="
            formId === 360 &&
            (salaryDetails?.revisionPayslipComponents?.length ||
              salaryDetails?.retiralsPayslipComponents?.length)
          "
          class="mt-6"
        >
          <h3>
            Arrear Details ( {{ selectedItem?.Effective_Month }} -
            {{ salaryDetails?.Payout_Month }})
          </h3>
          <v-card
            class="pa-2 mt-3"
            v-if="salaryDetails?.revisionPayslipComponents?.length"
          >
            <v-row>
              <v-col cols="9" class="bg-grey-lighten-3">Earnings</v-col>
              <v-col class="bg-grey-lighten-3 d-flex justify-start"
                >Amount</v-col
              >
            </v-row>
            <v-row
              v-for="(
                innerItems, index
              ) in salaryDetails.revisionPayslipComponents"
              :key="index"
            >
              <v-col cols="9" class="text-subtitle-2 no-padding">
                {{ innerItems.Allowance_Name }}
              </v-col>
              <v-col class="text-subtitle-2 d-flex justify-start">
                {{ innerItems.Amount?.toFixed(2) }}
              </v-col>
            </v-row>
            <v-divider></v-divider>
            <v-row class="mt-2">
              <v-col
                cols="9"
                class="text-subtitle-2 no-padding font-weight-bold"
              >
                Total Earnings
              </v-col>
              <v-col
                class="text-subtitle-2 d-flex justify-start font-weight-bold"
              >
                {{ totalEarnings }}
              </v-col>
            </v-row>
          </v-card>
          <v-card
            class="pa-2 mt-3"
            v-if="salaryDetails?.retiralsPayslipComponents?.length"
          >
            <v-row>
              <v-col cols="6" class="bg-grey-lighten-3">Retirals</v-col>
              <v-col class="bg-grey-lighten-3 d-flex justify-start"
                >Employer Share</v-col
              >
              <v-col class="bg-grey-lighten-3 d-flex justify-start"
                >Employee Share</v-col
              >
            </v-row>
            <v-row
              v-for="(
                innerItems, index
              ) in salaryDetails.retiralsPayslipComponents"
              :key="index"
            >
              <v-col cols="6" class="text-subtitle-2 no-padding">
                {{
                  "Arrear " +
                  (innerItems.Form_Id == 58
                    ? innerItems.Insurance_Name
                    : getCustomFormName(innerItems.Form_Id))
                }}
              </v-col>
              <v-col class="text-subtitle-2 d-flex justify-start">
                {{ innerItems.Employer_Share_Amount?.toFixed(2) }}
              </v-col>
              <v-col class="text-subtitle-2 d-flex justify-start">
                {{ innerItems.Employee_Share_Amount?.toFixed(2) }}
              </v-col>
            </v-row>
            <v-divider></v-divider>
            <v-row class="mt-2">
              <v-col
                cols="6"
                class="text-subtitle-2 no-padding font-weight-bold"
              >
                Total Retirals
              </v-col>
              <v-col
                class="text-subtitle-2 d-flex justify-start font-weight-bold"
              >
                {{ retiralsEmployerShareTotal }}
              </v-col>
              <v-col
                class="text-subtitle-2 d-flex justify-start font-weight-bold"
              >
                {{ retiralsEmployeeShareTotal }}
              </v-col>
            </v-row>
          </v-card>
        </div>
        <div v-if="moreDetailsList.length > 0" class="mt-6">
          <MoreDetails
            :more-details-list="moreDetailsList"
            :open-close-card="openMoreDetails"
            @on-open-close="openMoreDetails = $event"
          ></MoreDetails>
        </div>
        <div class="bottom-space"></div>
      </v-card-text>
    </v-card>
  </v-overlay>
</template>
<script>
import { checkNullValue } from "@/helper";
import moment from "moment";
import MoreDetails from "@/components/helper-components/MoreDetailsCard.vue";

export default {
  name: "ViewForm",
  emits: ["close-form", "open-edit-form"],
  components: {
    MoreDetails,
  },
  props: {
    showForm: {
      type: Boolean,
      default: false,
    },
    selectedItem: {
      type: Object,
      default: () => {},
    },
    payrollCurrency: {
      type: String,
      default: "",
    },
    formName: {
      type: String,
      default: "Salary Details",
    },
    formId: {
      type: Number,
      default: 207,
    },
  },
  data() {
    return {
      showOverlay: false,
      isLoading: false,
      salaryDetails: {},
      openMoreDetails: false,
      moreDetailsList: [],
      periodMap: {
        Monthly: [1, 12],
        Quarterly: [3, 4],
        HalfYearly: [6, 2],
        Annually: [12, 1],
      },
    };
  },
  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },
    windowWidth() {
      return this.$store.state.windowWidth;
    },
    overlayTitle() {
      let title = [
        this.selectedItem?.Employee_Name,
        this.selectedItem?.User_Defined_EmpId,
      ]
        .filter((item) => item)
        .join(" - ");
      return title ? title : "Salary Details";
    },
    getGroupName() {
      return (groupName) => {
        let name = "";
        if (groupName) {
          name = groupName.split("Array");
          if (name.length) {
            name = name[0];
            name = name.split(/(?=[A-Z])/).join(" ");
            name = name.charAt(0).toUpperCase() + name.slice(1);
          }
        }
        return name;
      };
    },
    getBonusText() {
      let formNames = [],
        total = 0;
      if (this.salaryDetails.allowances) {
        Object.keys(this.salaryDetails.allowances).forEach((key) => {
          if (this.salaryDetails.allowances[key].length) {
            this.salaryDetails.allowances[key]?.forEach((item) => {
              if (item.Form_Id?.includes(46)) {
                formNames.push(item.Allowance_Name);
                total += parseInt(item.Amount) || 0;
              }
            });
          }
        });
      }
      formNames = formNames.sort((a, b) => a.localeCompare(b));
      return formNames.length ? formNames.join(" + ") + " = " + total : "";
    },
    getCustomText() {
      return (formIds) => {
        let allowanceIds = formIds?.split(",");
        allowanceIds = new Set(allowanceIds);
        let formNames = [],
          total = 0;
        if (allowanceIds.size && this.salaryDetails.allowances) {
          Object.keys(this.salaryDetails.allowances).forEach((key) => {
            if (this.salaryDetails.allowances[key].length) {
              this.salaryDetails.allowances[key]?.forEach((item) => {
                if (allowanceIds.has(item.Allowance_Id.toString())) {
                  formNames.push(item.Allowance_Name);
                  total += parseInt(item.Amount) || 0;
                }
              });
            }
          });
        }
        return formNames.length ? formNames.join(" + ") + " = " + total : "";
      };
    },
    formsBasedOnFormId() {
      return this.$store.getters.formIdBasedAccessRights;
    },
    getCustomFormName() {
      return (formId) => {
        let form = this.formsBasedOnFormId(formId);
        return form ? form.customFormName : "";
      };
    },
    retiralsEmployeeShareTotal() {
      let total = 0;
      this.salaryDetails.retiralsPayslipComponents.forEach((item) => {
        total += item.Employee_Share_Amount;
      });
      return total?.toFixed(2);
    },
    retiralsEmployerShareTotal() {
      let total = 0;
      this.salaryDetails.retiralsPayslipComponents.forEach((item) => {
        total += item.Employer_Share_Amount;
      });
      return total?.toFixed(2);
    },
    totalEarnings() {
      let total = 0;
      this.salaryDetails.revisionPayslipComponents.forEach((item) => {
        total += parseInt(item.Amount) || 0;
      });
      return total?.toFixed(2);
    },
    componentWidth() {
      if (this.windowWidth > 1410) {
        return "40vw";
      } else if (this.windowWidth > 1264 && this.windowWidth < 1410) {
        return "50vw";
      } else if (this.windowWidth < 1264 && this.windowWidth > 810) {
        return "70vw";
      } else {
        return "100vw";
      }
    },
    formatDate() {
      return (date) => {
        if (date && moment(date).isValid()) {
          let orgDateFormat = this.$store.state.orgDetails.orgDateFormat;
          return date ? moment(date).format(orgDateFormat + " HH:mm:ss") : "";
        }
      };
    },
  },
  watch: {
    showForm(val) {
      this.showOverlay = val;
    },
  },
  mounted() {
    this.showOverlay = this.showForm;
    let apiVariables = {};
    if (this.formId == 207) {
      apiVariables = {
        formId: 207,
        isViewMode: true,
        id: null,
        employeeId: this.selectedItem?.Employee_Id,
      };
    } else if (this.formId == 206) {
      apiVariables = {
        formId: 206,
        isViewMode: true,
        templateId: this.selectedItem?.Template_Id,
      };
    } else if (this.formId == 360) {
      apiVariables = {
        formId: 360,
        isViewMode: true,
        id: this.selectedItem?.Revision_Id,
      };
    }
    this.getSalaryDetails(apiVariables);
  },
  methods: {
    checkNullValue,
    prefillMoreDetails() {
      this.moreDetailsList = [];
      let Added_On = "",
        Added_By = "",
        Updated_By = "",
        Updated_On = "";
      Added_On = this.formatDate(
        new Date(this.salaryDetails.Added_On + ".000Z")
      );
      Added_By = this.salaryDetails.AddedByName;
      Updated_By = this.salaryDetails.UpdatedByName;
      Updated_On = this.formatDate(
        new Date(this.salaryDetails.Updated_On + ".000Z")
      );

      if (Added_On && Added_By) {
        this.moreDetailsList.push({
          actionDate: Added_On,
          actionBy: Added_By,
          text: "Added",
        });
      }
      if (Updated_By && Updated_On) {
        this.moreDetailsList.push({
          actionDate: Updated_On,
          actionBy: Updated_By,
          text: "Updated",
        });
      }
    },
    async getSalaryDetails(apiVariables) {
      let vm = this;
      vm.isLoading = true;
      await vm.$store
        .dispatch("getSalaryDetails", apiVariables)
        .then(({ data }) => {
          if (data?.listSalaryTemplateDetails?.templateDetails) {
            let salaryList = JSON.parse(
              data.listSalaryTemplateDetails.templateDetails
            );
            if (salaryList.length) {
              vm.salaryDetails = salaryList[0];
              vm.salaryDetails.Payout_Month = moment(
                vm.salaryDetails.Payout_Month,
                "M,YYYY"
              ).isValid()
                ? moment(vm.salaryDetails.Payout_Month, "M,YYYY").format(
                    "MMM YYYY"
                  )
                : "";
              vm.salaryDetails.Effective_Month = moment(
                vm.salaryDetails.Effective_From
              ).isValid()
                ? moment(vm.salaryDetails.Effective_From).format("MMM YYYY")
                : "";
              vm.processData();
              vm.prefillMoreDetails();
            } else {
              vm.salaryDetails = {};
            }
          } else {
            vm.salaryDetails = {};
          }
          vm.isLoading = false;
        })
        .catch((err) => {
          vm.isLoading = false;
          vm.$store.dispatch("handleApiErrors", {
            error: err,
            action: "retrieving",
            form: "salary template details",
            isListError: false,
          });
        });
    },
    processData() {
      if (this.salaryDetails?.allowances?.basicPayArray?.length) {
        let basicPayObj = this.salaryDetails.allowances.basicPayArray[0];
        this.salaryDetails.basicPayName = basicPayObj.Allowance_Name;
        if (basicPayObj.Allowance_Type?.toLowerCase() === "percentage") {
          const mutiplier = basicPayObj.Percentage / 100;
          const basicPay = (
            (this.salaryDetails.Annual_CTC * mutiplier) /
            12
          ).toFixed(2);
          this.salaryDetails.Amount = basicPay ? basicPay : 0;
        } else {
          this.salaryDetails.Amount = basicPayObj.Amount;
        }
      } else {
        this.salaryDetails.Amount = 0;
        this.salaryDetails.basicPayName = "Basic Pay";
      }
      let keysToUpdate = [
        "allowanceArray",
        "flexiBenefitPlanArray",
        "reimbursementArray",
        "bonusArray",
      ];
      if (this.salaryDetails?.allowances) {
        keysToUpdate.map((item) => {
          this.salaryDetails.allowances[item]?.forEach((innerItems) => {
            if (!innerItems.Amount) {
              if (innerItems.Allowance_Type?.toLowerCase() === "percentage") {
                const mutiplier = innerItems.Percentage / 100;
                const basicPay = (
                  this.salaryDetails.Amount * mutiplier
                ).toFixed(2);
                innerItems.Amount = basicPay ? basicPay : 0;
              }
            }
          });
        });
      }
      if (this.salaryDetails?.retirals && this.salaryDetails.retirals.length) {
        this.salaryDetails.retirals.forEach((item) => {
          if (
            item.Retirals_Type?.toLowerCase() === "percentage" &&
            !item.Employer_Share_Amount
          ) {
            let percentage = item.Employer_Share_Percentage / 100 || 0;
            const mutiplier = item.Employer_Retiral_Wages || 0;
            const basicPay = (percentage * mutiplier).toFixed(2);
            item.Amount = basicPay ? basicPay : 0;
          } else {
            item.Percentage = null;
            item.Amount = Number(item.Employer_Share_Amount ?? 0);
          }
        });
      }
    },
  },
};
</script>
<style scoped>
.no-padding {
  padding-bottom: 0 !important;
}

.custom-info-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 15px;
  height: 15px;
  border: 1.5px solid #9e9e9e;
  border-radius: 50%;
}

.bottom-space {
  margin-top: 200px;
}
</style>
