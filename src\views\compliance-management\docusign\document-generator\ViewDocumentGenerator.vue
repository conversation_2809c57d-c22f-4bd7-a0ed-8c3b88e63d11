<template>
  <div>
    <v-card min-height="500" class="card-radius">
      <div
        class="d-flex align-center"
        style="justify-content: space-between; border-bottom: 2px solid #e5e5e5"
      >
        <div class="d-flex align-center text-primary font-weight-medium pl-4">
          <v-avatar
            class="mr-1"
            :class="[
              'avatarBCColor' +
                ((currentDocumentDetails.documentGeneratorId || 0) % 5),
            ]"
            size="35"
          >
            <span class="body-1 font-weight-medium">
              {{ letterAvatar(currentDocumentDetails.documentName, true) }}
            </span>
          </v-avatar>
          <div
            class="text-truncate"
            :style="isMobileView ? 'max-width: 150px' : 'max-width: 250px'"
          >
            {{ currentDocumentDetails.documentName }}
          </div>
        </div>
        <div class="pa-3 d-flex align-center">
          <!-- Close Button -->
          <v-icon color="primary" @click="closeView"> fas fa-times </v-icon>
        </div>
      </div>
      <v-card-text class="pa-6 bg-white">
        <!-- A4 Document Container -->
        <div
          class="d-flex justify-center overflow-auto"
          style="min-height: 600px"
        >
          <div
            class="bg-white rounded elevation-8 pa-12"
            style="width: 794px; min-height: 1123px; margin: 20px auto"
          >
            <div
              id="docContent"
              class="ck-content text-body-1"
              style="
                font-family: 'Times New Roman', serif;
                line-height: 1.6;
                min-height: calc(1123px - 192px);
              "
            />
          </div>
        </div>
      </v-card-text>
    </v-card>
  </div>
</template>

<script>
import { checkNullValue } from "@/helper";

export default {
  name: "ViewDocumentGenerator",

  props: {
    docGeneratorDetails: {
      type: Object,
      required: false,
      default: () => ({}),
    },
    formAccess: {
      type: [Object, Boolean],
      required: false,
      default: () => ({}),
    },
    docGeneratorContent: {
      type: String,
      default: "",
    },
  },

  data() {
    return {
      documentData: {},
      isLoading: false,
    };
  },

  computed: {
    isMobileView() {
      return this.$store.state.isMobileWindowSize;
    },

    // Use route data if available, otherwise use props
    currentDocumentDetails() {
      if (this.$route && this.$route.query && this.$route.query.documentData) {
        return this.documentData;
      }
      return this.docGeneratorDetails || {};
    },

    // Get form access from store if not provided as prop
    currentFormAccess() {
      if (this.formAccess && Object.keys(this.formAccess).length > 0) {
        return this.formAccess;
      }
      // Return empty object if no form access provided
      return {};
    },
  },

  watch: {
    docGeneratorContent: {
      handler() {
        this.$nextTick(() => {
          this.loadDocumentContent();
        });
      },
    },
  },

  mounted() {
    // Check if we have route parameters (navigated from list)
    if (this.$route && this.$route.query && this.$route.query.documentData) {
      try {
        this.documentData = JSON.parse(this.$route.query.documentData);
      } catch (error) {
        // Only redirect if we have router available
        if (this.$router) {
          this.$router.push({ name: "DocuSign" });
          return;
        }
      }
    }

    this.loadDocumentContent();
  },

  methods: {
    checkNullValue,

    // Method to return first letter in capital of each word (replaces deprecated filter)
    letterAvatar(value, isSingleLetter) {
      if (!value) return "";
      var firstChar = value ? value.charAt(0).toUpperCase() : "";
      var lastChar = value
        ? value.split(" ").pop().charAt(0).toUpperCase()
        : "";
      //condition checked for single letter avatar
      if (isSingleLetter) {
        return firstChar;
      } else {
        return firstChar + lastChar;
      }
    },

    // Navigation methods
    goBackToList() {
      this.$router.push({ name: "DocuSign" });
    },

    closeView() {
      // If we came from a route, go back to list
      if (this.$route && this.$route.query && this.$route.query.documentData) {
        this.goBackToList();
      } else {
        // If used as a component, emit close event
        this.$emit("close-view-form");
      }
    },

    // Print functionality
    printDocument() {
      // Use browser's native print functionality with current page
      window.print();
    },

    loadDocumentContent() {
      let content = this.docGeneratorContent;

      let element = document.getElementById("docContent");

      if (element) {
        // Check if content is a Promise object (which would display as [object Promise])
        if (
          content &&
          typeof content === "object" &&
          typeof content.then === "function"
        ) {
          element.innerHTML =
            '<p class="text-warning">Error loading document content. Please try again.</p>';
          return;
        }

        // Check if content is the string "[object Promise]"
        if (content === "[object Promise]") {
          element.innerHTML =
            '<p class="text-warning">Error loading document content. Please try again.</p>';
          return;
        }

        // Additional check for any object that might stringify to [object Promise]
        if (content && typeof content === "object") {
          element.innerHTML =
            '<p class="text-warning">Error: Content is an object instead of string.</p>';
          return;
        }

        // Set the content if it's valid
        if (content && typeof content === "string") {
          element.innerHTML = content;
        } else {
          element.innerHTML =
            '<p class="text-muted">No document content available.</p>';
        }
      }
    },
  },
};
</script>
