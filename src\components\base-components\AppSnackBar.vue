<template>
  <v-snackbar
    id="app_snackbar"
    v-model="showMessageBar"
    multi-line
    location="top"
    :timeout="timeOut"
    variant="outlined"
    :color="snackBarType"
    class="custom-snackbar"
  >
    <div
      v-if="snackBarMsg"
      class="d-flex align-center cursor-pointer text-subtitle-1 w-100 pa-0 ma-0"
      @click="showMessageBar = false"
    >
      <v-icon :color="snackBarType" size="25" class="ml-2 mr-2">{{
        snackbarIcon
      }}</v-icon>

      {{ snackBarMsg }}
      <v-spacer></v-spacer>
      <v-icon
        :color="snackBarType"
        size="25"
        class="mr-2"
        @click="showMessageBar = false"
      >
        fas fa-times
      </v-icon>
    </div>
    <slot name="custom-alert"></slot>
  </v-snackbar>
</template>

<script>
export default {
  name: "AppSnackBar",
  props: {
    showSnackBar: {
      type: Boolean,
      required: true,
    },
    snackBarMsg: {
      type: String,
      default: "",
    },
    // warning/error/success/info
    snackBarType: {
      type: String,
      default: "",
    },
    timeOut: {
      type: Number,
      default: 5000,
    },
  },

  data() {
    return {
      showMessageBar: false,
    };
  },
  computed: {
    snackbarIcon() {
      switch (this.snackBarType) {
        case "success":
          return "fas fa-check-circle";
        case "warning":
          return "fas fa-exclamation-circle";
        case "error":
          return "far fa-times-circle";
        default:
          return "fas fa-info-circle";
      }
    },
  },

  watch: {
    showMessageBar(val) {
      !val ? this.$emit("close-snack-bar") : "";
    },
  },
  mounted() {
    this.showMessageBar = this.showSnackBar;
    if (this.timeOut > 0) {
      setTimeout(() => {
        this.showMessageBar = false;
      }, this.timeOut);
    }
  },
};
</script>

<style lang="css">
.custom-snackbar {
  max-width: 100% !important;
  min-height: 48px !important;
  width: 100% !important;
  position: fixed !important;
  padding: 0px !important;
}

/* when vuetify upgrade if snackbar is not aligned properly adjust width, margin left and top properties */
#app_snackbar > .v-overlay__content {
  width: 100% !important;
  max-width: 100% !important;
  position: fixed !important;
  margin-top: 0px !important;
  margin-left: 0px !important;
  padding: 0px !important;
  left: 0px !important;
  right: 0px !important;
  background-color: white !important;
  box-shadow: 0px 1px 15px rgba(15, 84, 136, 0.14) !important;
}

.v-snackbar__wrapper {
  max-width: none !important;
  width: 100% !important;
  margin: 0px !important;
  padding: 0px !important;
}

/* Ensure full width and no padding for all screen sizes */
@media screen and (min-width: 320px) {
  #app_snackbar > .v-overlay__content {
    width: 100% !important;
    max-width: 100% !important;
    padding: 0px !important;
    margin-left: 0px !important;
    margin-top: 0px !important;
    left: 0px !important;
    right: 0px !important;
  }

  /* Ensure the snackbar container is also full width */
  .v-snackbar {
    width: 100% !important;
    left: 0px !important;
    right: 0px !important;
    padding: 0px !important;
  }
}

.v-snackbar--variant-outlined {
  border: 2px solid currentColor !important;
}

.v-snackbar--multi-line .v-snackbar__wrapper {
  min-height: 60px !important;
}
.text-secondary {
  color: rgb(var(--v-theme-primary)) !important;
}

/* Ensure custom alerts have full width and proper padding */
#app_snackbar .v-snackbar__content {
  width: 100% !important;
  padding: 0 !important;
  margin: 0 !important;
  left: 0 !important;
}

/* Fix for any nested elements in the snackbar */
#app_snackbar .v-snackbar__content > div {
  width: 100% !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

/* Remove any left padding/margin from the snackbar */
.v-snackbar {
  padding-left: 0 !important;
  margin-left: 0 !important;
  left: 0 !important;
}

/* Target the icon and message to ensure proper alignment */
#app_snackbar .v-icon.mr-2:first-child {
  margin-left: 8px !important;
}

/* Ensure the snackbar is positioned at the top with no left margin */
.v-overlay.v-snackbar {
  inset: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure the snackbar container takes full width */
.v-snackbar__wrapper {
  width: 100vw !important;
  max-width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
}
</style>
