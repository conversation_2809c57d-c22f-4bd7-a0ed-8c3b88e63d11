<template>
  <div style="height: 100%">
    <v-card class="pa-4 rounded-lg">
      <v-row>
        <v-col cols="12" class="py-2">
          <div class="d-flex align-center">
            <v-progress-circular
              model-value="100"
              color="yellow"
              :size="22"
              class="mr-2"
            />
            <span class="ml-2 text-primary text-h6 font-weight-bold">{{
              $t("dashboard.myTeamAction")
            }}</span>
          </div>
          <v-row class="pa-1">
            <v-col v-if="teamActionLoading" cols="12">
              <v-skeleton-loader
                v-for="i in 3"
                :key="i"
                class="mx-auto mt-4 mt-4"
                type="list-item-avatar-two-line"
              />
            </v-col>
            <v-col
              v-else-if="isNoTeamActions || errorInTeamAction"
              cols="12"
              md="10"
              class="py-2 mx-auto"
            >
              <div class="team-availability-content">
                <NoDataCardWithQuotes
                  id="team-action-error-no-data-card"
                  image-name="dashboard/actions-empty-image"
                  :primary-bold-text="$t('dashboard.lifeIs10Percent')"
                  :text-message="$t('dashboard.whatHappensToYou')"
                  :bottom-bold-text="$t('dashboard.and90Percent')"
                  :bottom-text-message="$t('dashboard.howYouReactToIt')"
                  :is-small-card="false"
                  :card-type="errorInTeamAction ? 'error' : 'no-data'"
                  :error-content="
                    errorInTeamAction ? $t('common.technicalDifficulties') : ''
                  "
                  image-size="80%"
                  :is-show-image="windowWidth > 960"
                  @refresh-triggered="refetchTeamActions()"
                />
              </div>
            </v-col>
            <v-col v-else cols="12" class="py-2 mx-auto">
              <perfect-scrollbar
                class="w-100 overflow-y-auto overflow-x-hidden team-action-scrollbar"
              >
                <div
                  id="team-action-probation-list"
                  class="team-availability-content"
                >
                  <PresentActionCards
                    v-for="(probation, index) in probationList"
                    :key="'probation' + index"
                    :title="probation.employeeName"
                    :list-index="index"
                    :is-clickable="false"
                  >
                    <template #avatarContent>
                      <div class="d-flex flex-column no-attendance-icon">
                        <v-avatar
                          class="text-xs-center justify-center avatarClass avatar_property"
                          :class="[
                            'avatarBCColor' + (probation.employeeId % 5),
                          ]"
                          size="40"
                        >
                          <span style="font-size: 13px">
                            {{ getEmployeeInitials(probation.employeeName) }}
                          </span>
                        </v-avatar>
                      </div>
                    </template>
                    <template #subTitleContent>
                      <div>
                        {{ formatDate(probation) }}
                      </div>
                    </template>
                    <template #actionContent>
                      <div class="d-flex align-center">
                        <span class="text-caption text-grey">{{
                          $t("dashboard.probation")
                        }}</span>
                        <v-btn
                          variant="elevated"
                          color="primary"
                          size="small"
                          rounded="lg"
                          class="ml-2"
                          @click="onUpdateProbation(probation)"
                        >
                          {{ $t("common.update") }}
                        </v-btn>
                      </div>
                    </template>
                  </PresentActionCards>
                </div>
              </perfect-scrollbar>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <ProbationActionModal
      v-if="openProbationActionModal"
      :open-modal="openProbationActionModal"
      :selected-probation="selectedProbation"
      @close-modal="closeProbationModal()"
      @update-success="handleProbationUpdateSuccess()"
    />
  </div>
</template>

<script>
import moment from "moment";
import NoDataCardWithQuotes from "@/components/helper-components/NoDataCardWithQuotes";
import PresentActionCards from "@/components/helper-components/PresentActionCards";
import ProbationActionModal from "./ProbationActionModal";
import { LIST_PROBATION_EMPLOYEES } from "@/graphql/dashboard/dashboardQueries";

export default {
  name: "MyTeamAction",

  components: {
    NoDataCardWithQuotes,
    PresentActionCards,
    ProbationActionModal,
  },

  props: {
    callingFrom: {
      type: String,
      required: true,
    },
  },

  data() {
    return {
      teamActionLoading: false,
      errorInTeamAction: false,
      selectedProbation: {},
      probationList: [],
      openProbationActionModal: false,
    };
  },

  computed: {
    windowWidth() {
      return this.$store.state.windowWidth;
    },

    isNoTeamActions() {
      return this.probationList.length === 0;
    },
    // formate probation date to present
    formatDate() {
      return (data) => {
        // check probation date is available, if yes then format and return it
        if (data.probationDate) {
          return moment(data.probationDate, "YYYY-MM-DD").format(
            "DD MMM, YYYY"
          );
        }
        // if probation date is returned as empty/null, then we can consider dateOfJoin as probation date
        else if (data.dateOfJoin) {
          // if probation days is 0, then consider dateOfJoin as probation date and format and return it
          if (!data.probationDays) {
            return moment(data.dateOfJoin, "YYYY-MM-DD").format("DD MMM, YYYY");
          }
          // if we have probation days, then we need to add those days with dateOfJoin to consider as probation date
          else {
            return moment(data.dateOfJoin, "YYYY-MM-DD")
              .add(parseInt(data.probationDays, 10), "days")
              .format("DD MMM, YYYY");
          }
        } else {
          return "-";
        }
      };
    },
  },

  mounted() {
    this.listProbationEmployee();
  },

  methods: {
    listProbationEmployee() {
      let vm = this;
      vm.teamActionLoading = true;
      vm.errorInTeamAction = false;
      vm.$apollo
        .query({
          query: LIST_PROBATION_EMPLOYEES,
          client: "apolloClientL",
          variables: {
            isTeamDashboard: this.callingFrom === "manager-dashboard" ? 1 : 0,
          },
        })
        .then((response) => {
          if (
            response.data &&
            response.data.listProbationEmployees &&
            response.data.listProbationEmployees.probationEmployeesDetails &&
            !response.data.listProbationEmployees.errorCode
          ) {
            let tempData =
              response.data.listProbationEmployees.probationEmployeesDetails;
            vm.probationList = tempData;
            vm.teamActionLoading = false;
          } else {
            vm.handleListError(
              response.data.listProbationEmployees?.errorCode || ""
            );
          }
        })
        .catch((err) => {
          vm.handleListError(err);
        });
    },
    handleListError(err = "") {
      this.teamActionLoading = false;
      this.$store
        .dispatch("handleApiErrors", {
          error: err,
          action: "retrieving",
          form: "dashboard",
          isListError: false,
        })
        .then((errorMessages) => {
          this.errorInTeamAction = errorMessages;
          this.isErrorInList = true;
        });
    },

    // Refetch team action data
    refetchTeamActions() {
      this.errorInTeamAction = false;
      this.listProbationEmployee();
    },
    closeProbationModal() {
      this.selectedProbation = {};
      this.openProbationActionModal = false;
    },
    onUpdateProbation(selected) {
      this.selectedProbation = selected;
      this.openProbationActionModal = true;
    },
    handleProbationUpdateSuccess() {
      this.closeProbationModal();
      this.refetchTeamActions();
    },

    // Get employee initials for avatar
    getEmployeeInitials(name) {
      if (!name) return "?";
      const names = name.split(" ");
      if (names.length >= 2) {
        return (names[0].charAt(0) + names[1].charAt(0)).toUpperCase();
      }
      return name.charAt(0).toUpperCase();
    },
  },
};
</script>

<style scoped>
/* Team action scrollbar height - responsive */
.team-action-scrollbar {
  height: 280px;
  max-height: 280px;
}

/* Perfect scrollbar styling */
:deep(.ps) {
  overflow-x: hidden !important;
}

:deep(.ps__rail-y) {
  background-color: transparent !important;
  opacity: 0.6;
}

:deep(.ps__thumb-y) {
  background-color: rgba(var(--v-theme-primary), 0.3) !important;
  border-radius: 4px;
}

:deep(.ps__rail-x) {
  display: none !important;
}

.team-availability-content {
  height: 20rem;
}

/* Responsive adjustments */
@media screen and (max-width: 600px) {
  .team-action-scrollbar {
    min-height: 280px;
    max-height: 500px;
  }
  .team-availability-content {
    min-height: 400px;
    max-height: 500px;
  }
}

@media screen and (max-width: 960px) and (min-width: 601px) {
  .team-action-scrollbar {
    height: 240px;
    max-height: 240px;
  }
}
</style>
